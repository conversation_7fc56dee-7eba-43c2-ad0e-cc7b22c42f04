package com.quadhub.feature.auth.ui.screens.dispatch

import androidx.compose.runtime.Immutable
import com.quadhub.compose.ui.element.image.Image
import com.quadhub.compose.ui.element.image.Images
import com.quadhub.compose.ui.element.text.Clause
import com.quadhub.compose.ui.element.text.Text
import com.quadhub.compose.ui.element.text.asText
import com.quadhub.core.presentation.navigation.NavigationCommand

internal object DispatchContract {

    interface Mapper {
        fun toResult(intent: Intent.Navigation): Result.OnNavigationCommandUpdated
    }

    sealed class Intent {

        data object OnGetStartButtonClick : Intent()
        data class SignInTextActionClick(val actionId: String) : Intent()
        sealed class Navigation : Intent() {
            data object Done : Navigation()
        }

    }

    sealed class Result {
        data class OnNavigationCommandUpdated(val navigationCommand: NavigationCommand?) : Result()
    }

    @Immutable
    data class ViewState(
        val navigationCommand: NavigationCommand?,
        val headerImage: Image,
        val headerText: Clause,
        val title: Clause,
        val subtitle: Clause,
        val socialIntroClause: Clause,
        val footerClause: Clause,
    ) {

        companion object {
            val Default = ViewState(
                navigationCommand = null,
                headerText = "quadhub.".asText(),
                headerImage = Images.logo,
                title = "Read. Post.\nShare your thoughts".asText(),
                subtitle = "quadhub is the easiest way to connect with students like you!".asText(),
                socialIntroClause = "Or continue using".asText(),
                footerClause = Text.listOf(
                    "Already have an account?".asText(),
                )
            )
        }

    }

}