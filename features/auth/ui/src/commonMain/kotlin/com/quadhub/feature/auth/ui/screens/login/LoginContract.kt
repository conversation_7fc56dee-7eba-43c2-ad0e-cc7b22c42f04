package com.quadhub.feature.auth.ui.screens.login

import com.quadhub.compose.ui.component.button.model.ButtonModel
import com.quadhub.compose.ui.component.button.model.ButtonState
import com.quadhub.compose.ui.component.button.model.asButton
import com.quadhub.compose.ui.element.text.Clause
import com.quadhub.compose.ui.element.text.asText
import com.quadhub.core.presentation.navigation.NavigationCommand

internal object LoginContract {
    interface Mapper{}

    sealed class Intent{}

    sealed class Result{}





    data class  ViewState(
        val navigationCommand: NavigationCommand?,
        val title: Clause,
        val subtitle: Clause,
        val pryBtn: ButtonModel,
        val btnState: ButtonState,
        val errorMessage: Clause? = null,
    ){
        val  Default = ViewState(
            navigationCommand = null,
            title = "Welcome to quadhub".asText(),
            subtitle = "Please sign in with your account.".asText(),
            pryBtn = "Sing in".asText().asButton(),
            btnState = ButtonState.Disabled
        )
    }
}