# QuadhubKMP

<details open="open">
<summary>Table of Contents</summary>

- [Overview](#overview)
    - [Purpose](#purpose)
    - [Technology Stack](#technology-stack)
- [Getting Started](#getting-started)
    - [Prerequisites](#prerequisites)
    - [Installation](#installation)
    - [Project Structure](#project-structure)
- [Contributing](#contributing)
- [Deployment Guide](#deployment-guide)

</details>

---

## Overview

This Kotlin Multiplatform (KMP) project enables seamless code sharing across Android and iOS, while
allowing platform-specific implementations where necessary. By leveraging KMP, we ensure
maintainability, efficiency, and a consistent experience across platforms.
Learn more
about [Kotlin Multiplatform](https://www.jetbrains.com/help/kotlin-multiplatform-dev/get-started.html).

## Purpose

The Quadhub is not just another social platform.....

## Technology Stack

**Programming Language:** [Kotlin](https://kotlinlang.org/)  
**Build System:** [Gradle (Kotlin DSL)](https://docs.gradle.org/current/userguide/kotlin_dsl.html)  
**UI:**
** [Jetpack Compose](https://developer.android.com/jetpack/compose), [Compose Multiplatform](https://www.jetbrains.com/lp/compose-multiplatform/)  
**Architecture:**

- [MVI](https://developer.android.com/topic/architecture): The app adopts the MVI pattern to manage
  UI state predictably, enabling a unidirectional data flow for better state management and
  debugging.
- [Clean Architecture](https://developer.android.com/topic/architecture): The project is structured
  using Clean Architecture principles, ensuring clear separation of concerns across different
  layers (Domain, Data, and Feature). This enhances scalability and testability while promoting code
  reusability.

**Multiplatform:** [Kotlin Multiplatform](https://kotlinlang.org/lp/multiplatform/)  
**Networking:** [Ktor](https://ktor.io/)  
**Dependency Injection:** [Kotlin Inject](https://github.com/evant/kotlin-inject)  
**Logging:** [Kermit](https://github.com/touchlab/Kermit)  
**Firebase:
** [Analytics](https://firebase.google.com/docs/analytics), [Crashlytics](https://firebase.google.com/docs/crashlytics), [BOM](https://firebase.google.com/docs/android/learn-more#bom)  
**Tooling:** [Kotlin Symbol Processing (KSP)](https://kotlinlang.org/docs/ksp-overview.html)  
**Jetpack Libraries:
** [Compose](https://developer.android.com/jetpack/compose), [Navigation](https://developer.android.com/guide/navigation), [ViewModel](https://developer.android.com/topic/libraries/architecture/viewmodel), [Activity](https://developer.android.com/reference/androidx/activity/package-summary), [Lifecycle](https://developer.android.com/jetpack/androidx/releases/lifecycle), [Material3](https://developer.android.com/jetpack/androidx/releases/compose-material3)  
**Annotation Processing:
** [Kotlin Symbol Processing (KSP)](https://kotlinlang.org/docs/ksp-overview.html)

# Getting Started

To get started with the Quadhub app. This section will walk you through setting up your development
environment, building the project, and running the application.

## Prerequisites

1. Operating System:

- macOS (recommended for iOS development)
- Windows (for Android Development)

2. Java Development Kit (JDK):

- Version: JDK 17 or higher is recommended.
- Installation: Follow the instructions for your operating system from the official Oracle JDK
  website or use a distribution like OpenJDK.
- Environment Variables: Verify that the JAVA_HOME environment variable is correctly set and points
  to your JDK installation directory.

3. Android Studio:

- Installation: Download [Android Studio Preview](https://developer.android.com/studio/preview) from
  the official Android Developer website and follow the installation instructions.
- Android SDK: During the Android Studio Preview setup, ensure that you install the necessary
  Android SDK components, including the Build Tools and Platform Tools.

4. Xcode (for iOS Development):

- Version: The latest stable release of Xcode is recommended.
- Installation: Install Xcode from the Mac App Store.
- Command Line Tools: Once Xcode is installed, open the terminal and run xcode-select --install to
  install the Xcode command line tools.

5. Git:

- Installation: Install Git from the official Git website or using your operating system's package
  manager.

6. Emulator/Physical Device:

- Android: Set up an Android Virtual Device (AVD) in Android Studio or use a physical Android
  device.
- iOS: Set up an iOS simulator in Xcode or use a physical iOS device.

## Installation

### Clone the Repository

Clone the project repository using the following command:

```bash
git clone https://....
cd QuadhubKMP
```

## Setup Android & iOS Environments

Ensure you have the required tools listed above installed:

- **Android Preview**: [Android Studio Preview](https://developer.android.com/studio/preview) and
  the latest SDK.
- **iOS**: Xcode and CocoaPods. You can install CocoaPods via:
  ```bash
  brew install cocoapods
  ```

## Build the Project

To build the project, use Gradle with the following command:

```bash
./gradlew build
```

## Open in Android Studio

1. **Launch Android Studio.**
2. **Click on "Open" (or "Open an Existing Project").**
3. **Navigate to the directory where you cloned the repository and select the project's root folder.
   **
4. **Click "OK".**

## Gradle Sync

* Android Studio will automatically start syncing the project with Gradle. This process downloads
  the necessary dependencies.
* **Troubleshooting:** If Gradle sync fails, check your internet connection and make sure that the
  JDK is properly configured. You can also try "File" -> "Invalidate Caches / Restart..." in Android
  Studio.

## Install missing SDK components

* If Android Studio requires additional SDK components, it will prompt you to install them. Follow
  the instructions in the prompt.

## Install Missing Plugins

* If Android Studio prompts you to install missing plugins, follow the instructions in the prompt.

## Trust the project

* If Android Studio prompts you to trust the project, you must accept to allow running code
  analysis.

## Building and Running the App

### Building:

* To build the project, you can use the Gradle tasks provided in Android Studio.
    1. Open the "Gradle" tool window (usually on the right side of the IDE).
    2. Navigate to `Tasks` -> `build` and double-click on `assembleDebug` or `assembleRelease` (
       depending on whether you want to build a debug or release version).
* You can run the task from the terminal with `./gradlew assembleDebug` or
  `./gradlew assembleRelease`.

### Running on Android:

1. **Select the `androidApp` configuration.**
2. **In Android Studio, select a target from the dropdown next to the run button. You can choose an
   emulator or a connected physical device.**
3. **Click the "Run" button (green triangle).**
4. **Android Studio will build the project and install the app on the selected target.**

* You can run the app from the terminal with `./gradlew :androidApp:run`.

### Running on iOS:

1. **Select the `iosApp` configuration.**
2. **In Android Studio, select a target from the dropdown next to the run button. You can choose a
   simulator or a connected physical device.**
3. **Click the "Run" button (green triangle).**
4. **Android Studio will build the project and install the app on the selected target.**

* You can run the app from the terminal with `./gradlew :iosApp:run`.

# Project Structure

This project is organized into a modular structure, utilizing Kotlin Multiplatform (KMP) to share
code between Android and iOS. Here's a breakdown of the key modules and their responsibilities:

## Explanation of Key Modules:

* **`compose-ui`:** This module houses all the UI-related code that is shared across platforms using
  Compose Multiplatform. It contains Composable functions, UI components, and themes.
* **`core`:** This set of modules contains fundamental logic and abstractions that are used
  throughout the application. The submodules cover presentation, database, common, and network.
* **`domain`:** This set of modules holds the business logic and domain-specific code for the
  application. The submodules cover database and network.
* **`features`:** This module contains the implementation of various features of the application,
  organized into submodules for better separation of concerns. Each submodule is responsible for a
  specific feature, including its UI and use cases.
* **`composeApp`:** This is the main application module for the project, structured to support both
  Android and iOS platforms using Kotlin Multiplatform. It includes platform-specific code and
  shared code, ensuring a consistent experience across platforms.
* **`iosApp`:** This is the iOS-specific application module. It depends on the `shared` module and
  contains code specific to the iOS platform.
* **`gradle`:** This directory contains the Gradle wrapper and configuration files. The
  `libs.versions.toml` contains all the dependencies version information.

**Key Characteristics:**

* **Modular:** The project is highly modular, promoting code reuse

## <summary> Modules</summary>

### `compose-ui`

The compose-ui module contains all the UI-related code that is shared across platforms using Compose
Multiplatform. It includes Composable functions, UI components, and themes. The module structure is
as follows:
<details>
<summary>Structure</summary>

- `src/`
    - `commonMain/`
        - `kotlin/` - Shared components across the entire app.
        - `resources/` - Shared resources across the entire app.

</details>

### `composeApp`

The composeApp module is the main application module for the project, structured to support both
Android and iOS platforms using Kotlin Multiplatform. It includes platform-specific code and shared
code, ensuring a consistent experience across platforms. The module structure is as follows:
<details>
<summary>Structure</summary>

- `src/`
    - `androidMain/`
        - `AndroidManifest.xml` - Android manifest file.
        - `res/` - Android resources.
        - `kotlin/` - Android-specific Kotlin code.
    - `commonMain/`
        - `kotlin/` - Shared Kotlin code.
        - `resources/` - Shared resources.
    - `iosMain/`
        - `kotlin/` - iOS-specific Kotlin code.
    - `commonTest/`
        - `kotlin/` - Shared test code.
    - `androidTest/`
        - `kotlin/` - Android-specific test code.
    - `iosTest/`
        - `kotlin/` - iOS-specific test code.
- `build.gradle.kts` - Gradle build script for the app module.

</details>

### `core`

The core module contains fundamental logic and abstractions used throughout the application. It is
divided into several submodules, each responsible for a specific aspect of the application's core
functionality. The module structure is as follows:
<details>
<summary>Structure</summary>

- `datetime/`
    - `src/` - Date and time handling code.
    - `build.gradle.kts` - Gradle build script for the datetime module.
- `discopes/`
    - `src/` - Discopes management code.
    - `build.gradle.kts` - Gradle build script for the discopes module.
- `file/`
    - `src/` - File handling code.
    - `build.gradle.kts` - Gradle build script for the file module.
- `location/`
    - `src/` - Location handling code.
    - `build.gradle.kts` - Gradle build script for the location module.
- `log`
    - `src/` - Logging code.
    - `build.gradle.kts` - Gradle build script for the log module.
- `network/`
    - `src/` - Network layer code.
    - `build.gradle.kts` - Gradle build script for the network module.
- `permissions/`
    - `src/` - Permissions handling code.
    - `build.gradle.kts` - Gradle build script for the permissions module.
- `presentation/`
    - `src/` - Presentation layer code.
    - `build.gradle.kts` - Gradle build script for the presentation module.
- `sharedpref/`
    - `src/` - Shared preferences code.
    - `build.gradle.kts` - Gradle build script for the shared preferences module.

</details>

### `domain`

The domain module encapsulates the business logic and domain-specific code for the application. It
is organized into several submodules, each responsible for a specific domain area, such as
interactions, database, authentication, and profile management. This structure ensures a clear
separation of concerns and promotes maintainability and scalability.
<details>
<summary>Structure</summary>
- `interactions/`
  - `src/` - Interactions domain code.
  - `build.gradle.kts` - Gradle build script for the interactions module.
</details>  

### `features`

The features module contains the implementation of various features of the application, organized
into submodules for better separation of concerns. Each submodule is responsible for a specific
feature, including its UI and use cases. The module structure is as follows:

**Directory structure:**
All features module follows same architecture, below is an example of a **Dispatch** feature in *
*Auth Module**

**Explanation:**

* **`features/`:** This is the root directory for feature modules.
* **`auth/`:** This is the module for authentication-related features.
    * **`/ui/`:** Contains UI-related code for the authentication feature.
        * **`di/`:**  Holds the dependency injection logic.
        * **`launcher/`:** Contains the launchers for different screens or views related to
          authentication.
        * **`navigation/`:** Holds the code for navigation.
        * **`ui/screens/`:** Holds the different screens or views related to authentication.
            * **`screens/dispatch/`:** A specific screen related to dispatch.
                * **`DispatchContract.kt`:** Defines the contract for the dispatch screen (e.g., UI
                  states, events).
                * **`DispatchInteractor.kt`:** Handles business logic for the Dispatch screen.
                * **`DispatchMapper.kt`:** Maps data for the dispatch screen.
                * **`DispatchReducer.kt`:** Manages state transformations for the Dispatch feature.
                * **`DispatchScreen.kt`:** The composable function for the dispatch screen.
                * **`DispatchViewModel.kt`:** Manages UI logic and data flow for the Dispatch
                  screen.
            * **`screens/dispatch/section/`:** Code separated by section for component that are used
              only with this module.
    * **`usecase/`:** Contains use cases related to authentication.
        * **`/di`:** Manages dependency injection (DI) for authentication use cases, providing
          instances of use case implementations.
        * **`/dto`:** Defines Data Transfer Objects (DTOs) used in authentication operations,
          representing structured data responses.
        * **`/usecases`:** Declares interfaces and contracts for authentication-related use cases,
          ensuring modularity and testability.
    * **`features/auth/build.gradle.kts`:** The build configuration for the `auth` feature module.

<details>
<summary>Modules</summary>
</details>

## Root Directory

- `README.md` - Project documentation.
- `build.gradle.kts` - Root Gradle build script.
- `settings.gradle.kts` - Gradle settings file.
- `gradle/` - Gradle wrapper files.
- `gradlew` - Unix shell script to run Gradle.
- `gradlew.bat` - Windows batch script to run Gradle.

## Contributing

Before getting started, please set up your development environment by following
the [Installation](#installation) section.

### Guidelines

- Follow the project's coding standards and architecture guidelines.
- Write clear commit messages and document your code where necessary.
- Ensure your changes do not break existing functionality by running tests.

### Submitting Changes

1**Create a Branch**

- Use a descriptive branch name related to your feature or fix.

   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Your Changes**

- Implement your feature or fix following best practices.
- Run tests to ensure your changes work as expected.

3. **Commit and Push**

- Write meaningful commit messages.

   ```bash
   git commit -m "Add feature description"
   git push origin feature/your-feature-name
   ```

4. **Create a Pull Request (PR)**

- Open a PR to the `main` branch.
- Provide a clear description of your changes and any relevant issue numbers.

5. **Review & Merge**

- Work with maintainers to address any feedback.
- Once approved, your PR will be merged into the project!

Thank you for contributing!

## Deployment Guide

This guide outlines the steps required to deploy the QuadHubKMP app for both Android and iOS
platforms. Ensure you follow these steps carefully to guarantee a smooth deployment process.

---

### Prerequisites

Before proceeding with deployment, ensure the following requirements are met:

- **Android:**
    - Android Studio Preview installed by following the [Installation](#installation) section.
    - Google Play Developer account access
    - Keystore file and credentials

- **iOS:**
    - Xcode installed
    - Apple Developer Program access
    - App Store Connect credentials
    - Provisioning profiles and certificates

- **General Requirements:**
    - Correctly configured Gradle scripts
    - Environment variables set up
    - Internet connection

---

### Android Deployment

#### 1. Build and Sign the APK/AAB

1. Open a terminal and navigate to the project root.
2. Run the following command to generate the release APK:
   ```bash
   ./gradlew assembleRelease
   ```
3. Alternatively, generate an AAB (Android App Bundle) for Play Store submission:
   ```bash
   ./gradlew bundleRelease
   ```
4. The output file will be located in:
   ```
   androidApp/build/outputs/
   ```

#### 2. Sign the APK/AAB

1. If not already configured, create a `keystore` using the following command:
   ```bash
   keytool -genkey -v -keystore release.keystore -alias quadhub -keyalg RSA -keysize 2048 -validity 10000
   ```
2. Sign the APK using the keystore:
   ```bash
   jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore release.keystore app-release.apk quadhub
   ```
3. Align and optimize the APK:
   ```bash
   zipalign -v 4 app-release.apk app-release-aligned.apk
   ```

#### 3. Upload to Google Play Console

1. Log in to [Google Play Console](https://play.google.com/console/)
2. Navigate to **App Releases** > **Production**
3. Upload the signed APK or AAB
4. Fill in the required store listing details
5. Submit for review and publish

---

### iOS Deployment

#### 1. Archive the App

1. Open the project in Xcode.
2. Select **Product** > **Archive**.
3. Once archiving completes, go to **Organizer**.

#### 2. Export and Sign the App

1. Click on the latest archive.
2. Select **Distribute App**.
3. Choose **App Store Connect** > **Upload**.
4. Ensure the correct provisioning profile is selected.
5. Click **Upload**.

#### 3. Submit via App Store Connect

1. Log in to [App Store Connect](https://appstoreconnect.apple.com/)
2. Navigate to **My Apps** > **QuadHubKMP**
3. Select **Prepare for Submission**
4. Fill in necessary details (screenshots, description, etc.)
5. Submit for Apple review and wait for approval

---

### Post-Deployment Steps

- Monitor crash reports in **Firebase Crashlytics**
- Track analytics using **Google Analytics**
- Ensure proper rollout through staged releases
- Gather feedback and plan for future updates

This concludes the deployment guide for the QuadHubKMP app. Follow these steps carefully to ensure a
seamless release.

# Pipeline test
