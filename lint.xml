<?xml version="1.0" encoding="UTF-8"?>
<lint>
    <!-- Disable NonNullableMutableLiveDataDetector due to Kotlin 2.1.21 compatibility issue -->
    <!-- This detector causes IncompatibleClassChangeError with current Kotlin/AGP versions -->
    <issue id="NullSafeMutableLiveData" severity="ignore" />
    <issue id="NonNullableMutableLiveData" severity="ignore" />

    <!-- Disable MissingClass for development - classes may not be implemented yet -->
    <issue id="MissingClass" severity="warning" />

    <!-- Disable other problematic detectors that may cause similar issues -->
    <issue id="UnusedResources" severity="warning" />
    <issue id="IconMissingDensityFolder" severity="warning" />
    <issue id="IconDensities" severity="warning" />
    
    <!-- Keep important checks enabled -->
    <issue id="HardcodedText" severity="warning" />
    <issue id="MissingTranslation" severity="warning" />
    <issue id="UnusedAttribute" severity="warning" />
</lint>
