+ umask 000

+ export GIT_LFS_SKIP_SMUDGE=1

+ retry 6 git clone --branch="main" --depth 50 https://x-token-auth:$<EMAIL>/$BITBUCKET_REPO_FULL_NAME.git $BUILD_DIR
Cloning into '/opt/atlassian/pipelines/agent/build'...

+ git reset --hard b7ad29639d64a5b8cf0bad4166107b6b4b709998
HEAD is now at b7ad296 Fixed pipeline error:wget: command not found

+ git config user.name bitbucket-pipelines

+ git config user.email <EMAIL>

+ git config push.default current

+ git config http.${BITBUCKET_GIT_HTTP_ORIGIN}.proxy http://localhost:29418/

+ git remote set-url origin http://bitbucket.org/$BITBUCKET_REPO_FULL_NAME

+ git reflog expire --expire=all --all

+ echo ".bitbucket/pipelines/generated" >> .git/info/exclude

+ chmod 777 $BUILD_DIR


Cache "gradle: ~/.gradle/caches": Downloading
Cache "gradle: ~/.gradle/caches": Not found
Cache "gradle-wrapper: ~/.gradle/wrapper": Downloading
Cache "gradle-wrapper: ~/.gradle/wrapper": Not found

Default variables:
    BITBUCKET_BRANCH
    BITBUCKET_BUILD_NUMBER
    BITBUCKET_CLONE_DIR
    BITBUCKET_COMMIT
    BITBUCKET_GIT_HTTP_ORIGIN
    BITBUCKET_GIT_SSH_ORIGIN
    BITBUCKET_PARALLEL_STEP
    BITBUCKET_PARALLEL_STEP_COUNT
    BITBUCKET_PIPELINES_VARIABLES_PATH
    BITBUCKET_PIPELINE_UUID
    BITBUCKET_PROJECT_KEY
    BITBUCKET_PROJECT_UUID
    BITBUCKET_REPO_FULL_NAME
    BITBUCKET_REPO_IS_PRIVATE
    BITBUCKET_REPO_OWNER
    BITBUCKET_REPO_OWNER_UUID
    BITBUCKET_REPO_SLUG
    BITBUCKET_REPO_UUID
    BITBUCKET_SSH_KEY_FILE
    BITBUCKET_STEP_RUN_NUMBER
    BITBUCKET_STEP_TRIGGERER_UUID
    BITBUCKET_STEP_UUID
    BITBUCKET_WORKSPACE
    CI
    DOCKER_HOST
    PIPELINES_JWT_TOKEN

Images used:
    build : docker.io/library/eclipse-temurin@sha256:2cc80b4288f6240734ecd1acae01d20afa56f86327b9ae207a9468363422c974

Runtime:
    cloud:
        version: 2
        arch: x86
+ apt-get update
Get:1 http://archive.ubuntu.com/ubuntu noble InRelease [256 kB]
Get:2 http://security.ubuntu.com/ubuntu noble-security InRelease [126 kB]
Get:3 http://archive.ubuntu.com/ubuntu noble-updates InRelease [126 kB]
Get:4 http://security.ubuntu.com/ubuntu noble-security/main amd64 Packages [1,105 kB]
Get:5 http://archive.ubuntu.com/ubuntu noble-backports InRelease [126 kB]
Get:6 http://archive.ubuntu.com/ubuntu noble/main amd64 Packages [1,808 kB]
Get:7 http://archive.ubuntu.com/ubuntu noble/restricted amd64 Packages [117 kB]
Get:8 http://archive.ubuntu.com/ubuntu noble/universe amd64 Packages [19.3 MB]
Get:9 http://security.ubuntu.com/ubuntu noble-security/multiverse amd64 Packages [22.1 kB]
Get:10 http://security.ubuntu.com/ubuntu noble-security/universe amd64 Packages [1,106 kB]
Get:11 http://security.ubuntu.com/ubuntu noble-security/restricted amd64 Packages [1,454 kB]
Get:12 http://archive.ubuntu.com/ubuntu noble/multiverse amd64 Packages [331 kB]
Get:13 http://archive.ubuntu.com/ubuntu noble-updates/multiverse amd64 Packages [26.7 kB]
Get:14 http://archive.ubuntu.com/ubuntu noble-updates/universe amd64 Packages [1,403 kB]
Get:15 http://archive.ubuntu.com/ubuntu noble-updates/main amd64 Packages [1,425 kB]
Get:16 http://archive.ubuntu.com/ubuntu noble-updates/restricted amd64 Packages [1,509 kB]
Get:17 http://archive.ubuntu.com/ubuntu noble-backports/main amd64 Packages [48.0 kB]
Get:18 http://archive.ubuntu.com/ubuntu noble-backports/universe amd64 Packages [31.8 kB]
Fetched 30.3 MB in 4s (7,716 kB/s)
Reading package lists...

+ apt-get install -y wget unzip curl
Reading package lists...
Building dependency tree...
Reading state information...
wget is already the newest version (1.21.4-1ubuntu4.1).
curl is already the newest version (8.5.0-2ubuntu10.6).
Suggested packages:
  zip
The following NEW packages will be installed:
  unzip
0 upgraded, 1 newly installed, 0 to remove and 2 not upgraded.
Need to get 174 kB of archives.
After this operation, 384 kB of additional disk space will be used.
Get:1 http://archive.ubuntu.com/ubuntu noble-updates/main amd64 unzip amd64 6.0-28ubuntu4.1 [174 kB]
debconf: delaying package configuration, since apt-utils is not installed
Fetched 174 kB in 1s (229 kB/s)
Selecting previously unselected package unzip.
(Reading database ... 
(Reading database ... 5%
(Reading database ... 10%
(Reading database ... 15%
(Reading database ... 20%
(Reading database ... 25%
(Reading database ... 30%
(Reading database ... 35%
(Reading database ... 40%
(Reading database ... 45%
(Reading database ... 50%
(Reading database ... 55%
(Reading database ... 60%
(Reading database ... 65%
(Reading database ... 70%
(Reading database ... 75%
(Reading database ... 80%
(Reading database ... 85%
(Reading database ... 90%
(Reading database ... 95%
(Reading database ... 100%
(Reading database ... 6864 files and directories currently installed.)
Preparing to unpack .../unzip_6.0-28ubuntu4.1_amd64.deb ...
Unpacking unzip (6.0-28ubuntu4.1) ...
Setting up unzip (6.0-28ubuntu4.1) ...

+ export ANDROID_HOME=/opt/android-sdk

+ export ANDROID_SDK_ROOT=/opt/android-sdk

+ export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin

+ mkdir -p $ANDROID_HOME

+ cd $ANDROID_HOME

+ wget -q https://dl.google.com/android/repository/commandlinetools-linux-11076708_latest.zip

+ unzip -q commandlinetools-linux-11076708_latest.zip

+ mkdir -p cmdline-tools/latest

+ mv cmdline-tools/* cmdline-tools/latest/ 2>/dev/null || true

+ yes | $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager --licenses > /dev/null 2>&1 || true

+ $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager "platform-tools" "platforms;android-36"
Loading package information...                                                  
Loading local repository...                                                     
[                                       ] 3% Loading local repository...        
[                                       ] 3% Fetch remote repository...         
[=                                      ] 3% Fetch remote repository...         
[=                                      ] 4% Fetch remote repository...         
[=                                      ] 5% Fetch remote repository...         
[==                                     ] 5% Fetch remote repository...         
[==                                     ] 6% Fetch remote repository...         
[==                                     ] 7% Fetch remote repository...         
[==                                     ] 7% Computing updates...               
[===                                    ] 8% Computing updates...               
[===                                    ] 10% Computing updates...              
[===                                    ] 10% Installing Android SDK Platform 36
[===                                    ] 10% Downloading platform-36_r02.zip...
[====                                   ] 10% Downloading platform-36_r02.zip...
[====                                   ] 11% Downloading platform-36_r02.zip...
[====                                   ] 12% Downloading platform-36_r02.zip...
[=====                                  ] 13% Downloading platform-36_r02.zip...
[=====                                  ] 14% Downloading platform-36_r02.zip...
[=====                                  ] 15% Downloading platform-36_r02.zip...
[======                                 ] 15% Downloading platform-36_r02.zip...
[======                                 ] 16% Downloading platform-36_r02.zip...
[======                                 ] 17% Downloading platform-36_r02.zip...
[=======                                ] 18% Downloading platform-36_r02.zip...
[=======                                ] 19% Downloading platform-36_r02.zip...
[=======                                ] 20% Downloading platform-36_r02.zip...
[========                               ] 20% Downloading platform-36_r02.zip...
[========                               ] 21% Downloading platform-36_r02.zip...
[========                               ] 21% Unzipping...                      
[========                               ] 21% Unzipping... android-36/android-st
[========                               ] 22% Unzipping... android-36/android-st
[=========                              ] 23% Unzipping... android-36/android-st
[=========                              ] 23% Unzipping... android-36/android.ja
[=========                              ] 24% Unzipping... android-36/android.ja
[=========                              ] 25% Unzipping... android-36/android.ja
[==========                             ] 25% Unzipping... android-36/android.ja
[==========                             ] 26% Unzipping... android-36/android.ja
[==========                             ] 27% Unzipping... android-36/android.ja
[==========                             ] 27% Unzipping... android-36/build.prop
[==========                             ] 27% Unzipping... android-36/core-for-s
[==========                             ] 27% Unzipping... android-36/data/NOTIC
[==========                             ] 27% Unzipping... android-36/data/activ
[==========                             ] 27% Unzipping... android-36/data/annot
[==========                             ] 27% Unzipping... android-36/data/api-v
[==========                             ] 27% Unzipping... android-36/data/broad
[==========                             ] 27% Unzipping... android-36/data/categ
[==========                             ] 27% Unzipping... android-36/data/featu
[==========                             ] 27% Unzipping... android-36/data/res/a
[==========                             ] 27% Unzipping... android-36/data/res/c
[==========                             ] 27% Unzipping... android-36/data/res/d
[===========                            ] 28% Unzipping... android-36/data/res/d
[===========                            ] 29% Unzipping... android-36/data/res/d
[===========                            ] 30% Unzipping... android-36/data/res/d
[===========                            ] 30% Unzipping... android-36/data/res/i
[===========                            ] 30% Unzipping... android-36/data/res/l
[============                           ] 30% Unzipping... android-36/data/res/l
[============                           ] 30% Unzipping... android-36/data/res/m
[============                           ] 30% Unzipping... android-36/data/res/r
[============                           ] 30% Unzipping... android-36/data/res/t
[============                           ] 30% Unzipping... android-36/data/res/v
[============                           ] 31% Unzipping... android-36/data/res/v
[============                           ] 32% Unzipping... android-36/data/res/v
[============                           ] 32% Unzipping... android-36/data/res/x
[============                           ] 32% Unzipping... android-36/data/servi
[============                           ] 32% Unzipping... android-36/data/widge
[============                           ] 32% Unzipping... android-36/framework.
[============                           ] 32% Unzipping... android-36/optional/a
[============                           ] 32% Unzipping... android-36/optional/l
[============                           ] 32% Unzipping... android-36/optional/o
[============                           ] 32% Unzipping... android-36/optional/w
[============                           ] 32% Unzipping... android-36/sdk.proper
[============                           ] 32% Unzipping... android-36/skins/HVGA
[============                           ] 32% Unzipping... android-36/skins/NOTI
[============                           ] 32% Unzipping... android-36/skins/QVGA
[============                           ] 32% Unzipping... android-36/skins/WQVG
[============                           ] 32% Unzipping... android-36/skins/WSVG
[============                           ] 32% Unzipping... android-36/skins/WVGA
[============                           ] 32% Unzipping... android-36/skins/WXGA
[============                           ] 32% Unzipping... android-36/source.pro
[============                           ] 32% Unzipping... android-36/templates/
[============                           ] 32% Unzipping... android-36/uiautomato
[=============                          ] 33% Unzipping... android-36/uiautomato
[=====================                  ] 55% Unzipping... android-36/uiautomato
[=====================                  ] 55% Installing Android SDK Platform-To
[=====================                  ] 55% Downloading platform-tools_r35.0.2
[======================                 ] 55% Downloading platform-tools_r35.0.2
[======================                 ] 56% Downloading platform-tools_r35.0.2
[======================                 ] 57% Downloading platform-tools_r35.0.2
[=======================                ] 58% Downloading platform-tools_r35.0.2
[=======================                ] 59% Downloading platform-tools_r35.0.2
[=======================                ] 60% Downloading platform-tools_r35.0.2
[========================               ] 60% Downloading platform-tools_r35.0.2
[========================               ] 61% Downloading platform-tools_r35.0.2
[========================               ] 62% Downloading platform-tools_r35.0.2
[=========================              ] 63% Downloading platform-tools_r35.0.2
[=========================              ] 64% Downloading platform-tools_r35.0.2
[=========================              ] 65% Downloading platform-tools_r35.0.2
[==========================             ] 65% Downloading platform-tools_r35.0.2
[==========================             ] 66% Downloading platform-tools_r35.0.2
[==========================             ] 66% Unzipping... android-36/uiautomato
[==========================             ] 66% Unzipping... platform-tools/NOTICE
[==========================             ] 67% Unzipping... platform-tools/NOTICE
[==========================             ] 67% Unzipping... platform-tools/adb   
[===========================            ] 68% Unzipping... platform-tools/adb   
[===========================            ] 69% Unzipping... platform-tools/adb   
[===========================            ] 70% Unzipping... platform-tools/adb   
[============================           ] 70% Unzipping... platform-tools/adb   
[============================           ] 71% Unzipping... platform-tools/adb   
[============================           ] 72% Unzipping... platform-tools/adb   
[============================           ] 72% Unzipping... platform-tools/etc1to
[============================           ] 72% Unzipping... platform-tools/fastbo
[=============================          ] 73% Unzipping... platform-tools/fastbo
[=============================          ] 73% Unzipping... platform-tools/hprof-
[=============================          ] 73% Unzipping... platform-tools/make_f
[=============================          ] 74% Unzipping... platform-tools/make_f
[=============================          ] 74% Unzipping... platform-tools/mke2fs
[=============================          ] 74% Unzipping... platform-tools/source
[=============================          ] 74% Unzipping... platform-tools/sqlite
[=============================          ] 75% Unzipping... platform-tools/sqlite
[==============================         ] 75% Unzipping... platform-tools/sqlite
[==============================         ] 76% Unzipping... platform-tools/sqlite
[==============================         ] 77% Unzipping... platform-tools/sqlite
[==============================         ] 77% Unzipping... platform-tools/lib64/
[==============================         ] 78% Unzipping... platform-tools/lib64/
[=======================================] 100% Unzipping... platform-tools/lib64


+ cd $BITBUCKET_CLONE_DIR

+ chmod +x ./gradlew

+ ./gradlew lint --no-daemon --stacktrace
Downloading https://services.gradle.org/distributions/gradle-8.9-bin.zip
............10%.............20%.............30%.............40%.............50%.............60%.............70%.............80%.............90%.............100%

Welcome to Gradle 8.9!

Here are the highlights of this release:
 - Enhanced Error and Warning Messages
 - IDE Integration Improvements
 - Daemon JVM Information

For more details see https://docs.gradle.org/8.9/release-notes.html

To honour the JVM settings for this build a single-use Daemon process will be forked. For more on this, please refer to https://docs.gradle.org/8.9/userguide/gradle_daemon.html#sec:disabling_the_daemon in the Gradle documentation.
Daemon will be stopped at the end of the build 
Calculating task graph as no cached configuration is available for tasks: lint
Type-safe project accessors is an incubating feature.

> Configure project :compose-ui
w: ⚠️ Disabled Kotlin/Native Targets
The following Kotlin/Native targets cannot be built on this machine and are disabled:
iosArm64, iosSimulatorArm64, iosX64
To hide this message, add 'kotlin.native.ignoreDisabledTargets=true' to the Gradle properties.



> Configure project :composeApp
w: ⚠️ Disabled Kotlin/Native Targets
The following Kotlin/Native targets cannot be built on this machine and are disabled:
iosArm64, iosSimulatorArm64, iosX64
To hide this message, add 'kotlin.native.ignoreDisabledTargets=true' to the Gradle properties.



> Configure project :core:auth
w: ⚠️ Disabled Kotlin/Native Targets
The following Kotlin/Native targets cannot be built on this machine and are disabled:
iosArm64, iosSimulatorArm64, iosX64
To hide this message, add 'kotlin.native.ignoreDisabledTargets=true' to the Gradle properties.



> Configure project :core:datetime
w: ⚠️ Disabled Kotlin/Native Targets
The following Kotlin/Native targets cannot be built on this machine and are disabled:
iosArm64, iosSimulatorArm64, iosX64
To hide this message, add 'kotlin.native.ignoreDisabledTargets=true' to the Gradle properties.



> Configure project :core:discopes
w: ⚠️ Disabled Kotlin/Native Targets
The following Kotlin/Native targets cannot be built on this machine and are disabled:
iosArm64, iosSimulatorArm64, iosX64
To hide this message, add 'kotlin.native.ignoreDisabledTargets=true' to the Gradle properties.



> Configure project :core:file
w: ⚠️ Disabled Kotlin/Native Targets
The following Kotlin/Native targets cannot be built on this machine and are disabled:
iosArm64, iosSimulatorArm64, iosX64
To hide this message, add 'kotlin.native.ignoreDisabledTargets=true' to the Gradle properties.



> Configure project :core:location
w: ⚠️ Disabled Kotlin/Native Targets
The following Kotlin/Native targets cannot be built on this machine and are disabled:
iosArm64, iosSimulatorArm64, iosX64
To hide this message, add 'kotlin.native.ignoreDisabledTargets=true' to the Gradle properties.



> Configure project :core:log
w: ⚠️ Disabled Kotlin/Native Targets
The following Kotlin/Native targets cannot be built on this machine and are disabled:
iosArm64, iosSimulatorArm64, iosX64
To hide this message, add 'kotlin.native.ignoreDisabledTargets=true' to the Gradle properties.



> Configure project :core:network
w: ⚠️ Disabled Kotlin/Native Targets
The following Kotlin/Native targets cannot be built on this machine and are disabled:
iosArm64, iosSimulatorArm64, iosX64
To hide this message, add 'kotlin.native.ignoreDisabledTargets=true' to the Gradle properties.



> Configure project :core:permissions
w: ⚠️ Disabled Kotlin/Native Targets
The following Kotlin/Native targets cannot be built on this machine and are disabled:
iosArm64, iosSimulatorArm64, iosX64
To hide this message, add 'kotlin.native.ignoreDisabledTargets=true' to the Gradle properties.



> Configure project :core:player
w: ⚠️ Disabled Kotlin/Native Targets
The following Kotlin/Native targets cannot be built on this machine and are disabled:
iosArm64, iosSimulatorArm64, iosX64
To hide this message, add 'kotlin.native.ignoreDisabledTargets=true' to the Gradle properties.



> Configure project :core:presentation
w: ⚠️ Disabled Kotlin/Native Targets
The following Kotlin/Native targets cannot be built on this machine and are disabled:
iosArm64, iosSimulatorArm64, iosX64
To hide this message, add 'kotlin.native.ignoreDisabledTargets=true' to the Gradle properties.



> Configure project :core:recorder
w: ⚠️ Disabled Kotlin/Native Targets
The following Kotlin/Native targets cannot be built on this machine and are disabled:
iosArm64, iosSimulatorArm64, iosX64
To hide this message, add 'kotlin.native.ignoreDisabledTargets=true' to the Gradle properties.



> Configure project :core:sharedpref
w: ⚠️ Disabled Kotlin/Native Targets
The following Kotlin/Native targets cannot be built on this machine and are disabled:
iosArm64, iosSimulatorArm64, iosX64
To hide this message, add 'kotlin.native.ignoreDisabledTargets=true' to the Gradle properties.



> Configure project :features:auth:ui
w: ⚠️ Disabled Kotlin/Native Targets
The following Kotlin/Native targets cannot be built on this machine and are disabled:
iosArm64, iosSimulatorArm64, iosX64
To hide this message, add 'kotlin.native.ignoreDisabledTargets=true' to the Gradle properties.



> Configure project :features:auth:usecase
w: ⚠️ Disabled Kotlin/Native Targets
The following Kotlin/Native targets cannot be built on this machine and are disabled:
iosArm64, iosSimulatorArm64, iosX64
To hide this message, add 'kotlin.native.ignoreDisabledTargets=true' to the Gradle properties.


WARNING: We recommend using a newer Android Gradle plugin to use compileSdk = 36

This Android Gradle plugin (8.7.3) was tested up to compileSdk = 35.

You are strongly encouraged to update your project to use a newer
Android Gradle plugin that has been tested with compileSdk = 36.

If you are already using the latest version of the Android Gradle plugin,
you may need to wait until a newer version with support for compileSdk = 36 is available.

For more information refer to the compatibility table:
https://d.android.com/r/tools/api-level-support

To suppress this warning, add/update
    android.suppressUnsupportedCompileSdk=36
to this project's gradle.properties.
Checking the license for package Android SDK Build-Tools 34 in /opt/android-sdk/licenses
License for package Android SDK Build-Tools 34 accepted.
Preparing "Install Android SDK Build-Tools 34 v.34.0.0".
"Install Android SDK Build-Tools 34 v.34.0.0" ready.
Installing Android SDK Build-Tools 34 in /opt/android-sdk/build-tools/34.0.0
"Install Android SDK Build-Tools 34 v.34.0.0" complete.
"Install Android SDK Build-Tools 34 v.34.0.0" finished.

> Task :compose-ui:convertXmlValueResourcesForAppleMain NO-SOURCE
> Task :compose-ui:convertXmlValueResourcesForAndroidDebug NO-SOURCE
> Task :compose-ui:convertXmlValueResourcesForAndroidMain NO-SOURCE
> Task :compose-ui:copyNonXmlValueResourcesForAppleMain NO-SOURCE
> Task :compose-ui:prepareComposeResourcesTaskForAppleMain NO-SOURCE
> Task :compose-ui:copyNonXmlValueResourcesForAndroidDebug NO-SOURCE
> Task :compose-ui:copyNonXmlValueResourcesForAndroidMain NO-SOURCE
> Task :compose-ui:generateResourceAccessorsForAppleMain NO-SOURCE
> Task :compose-ui:convertXmlValueResourcesForIosMain NO-SOURCE
> Task :compose-ui:prepareComposeResourcesTaskForAndroidMain NO-SOURCE
> Task :compose-ui:copyNonXmlValueResourcesForIosMain NO-SOURCE
> Task :compose-ui:prepareComposeResourcesTaskForAndroidDebug NO-SOURCE
> Task :compose-ui:generateResourceAccessorsForAndroidMain NO-SOURCE
> Task :compose-ui:prepareComposeResourcesTaskForIosMain NO-SOURCE
> Task :compose-ui:generateResourceAccessorsForIosMain NO-SOURCE
> Task :compose-ui:convertXmlValueResourcesForNativeMain NO-SOURCE
> Task :compose-ui:preBuild UP-TO-DATE
> Task :compose-ui:copyNonXmlValueResourcesForNativeMain NO-SOURCE
> Task :compose-ui:prepareComposeResourcesTaskForNativeMain NO-SOURCE
> Task :compose-ui:generateResourceAccessorsForNativeMain NO-SOURCE
> Task :compose-ui:preDebugBuild UP-TO-DATE
> Task :core:datetime:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :compose-ui:generateResourceAccessorsForAndroidDebug NO-SOURCE
> Task :core:datetime:jvmProcessResources NO-SOURCE
> Task :core:datetime:processJvmMainResources SKIPPED
> Task :compose-ui:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :compose-ui:copyNonXmlValueResourcesForCommonMain
> Task :compose-ui:convertXmlValueResourcesForCommonMain NO-SOURCE
> Task :compose-ui:prepareComposeResourcesTaskForCommonMain
> Task :compose-ui:mergeDebugJniLibFolders
> Task :compose-ui:generateComposeResClass
> Task :compose-ui:mergeDebugNativeLibs NO-SOURCE
> Task :compose-ui:stripDebugDebugSymbols NO-SOURCE
> Task :compose-ui:copyDebugComposeResourcesToAndroidAssets
> Task :compose-ui:generateDebugResValues
> Task :compose-ui:generateDebugResources
> Task :compose-ui:copyDebugJniLibsProjectAndLocalJars
> Task :compose-ui:generateExpectResourceCollectorsForCommonMain
> Task :compose-ui:extractDeepLinksForAarDebug
> Task :compose-ui:generateResourceAccessorsForCommonMain
> Task :compose-ui:javaPreCompileDebug
> Task :compose-ui:generateActualResourceCollectorsForAndroidMain
> Task :compose-ui:packageDebugResources
> Task :compose-ui:debugAssetsCopyForAGP NO-SOURCE
> Task :compose-ui:mergeDebugShaders
> Task :compose-ui:prepareDebugArtProfile
> Task :compose-ui:compileDebugShaders NO-SOURCE
> Task :compose-ui:generateDebugAssets UP-TO-DATE
> Task :compose-ui:prepareLintJarForPublish
> Task :compose-ui:packageDebugAssets
> Task :compose-ui:writeDebugAarMetadata
> Task :compose-ui:preDebugAndroidTestBuild UP-TO-DATE
> Task :compose-ui:generateDebugAndroidTestResValues FROM-CACHE
> Task :compose-ui:writeDebugLintModelMetadata
> Task :compose-ui:extractProguardFiles
> Task :compose-ui:preDebugUnitTestBuild UP-TO-DATE
> Task :composeApp:convertXmlValueResourcesForAndroidDebug NO-SOURCE
> Task :composeApp:copyNonXmlValueResourcesForAndroidDebug NO-SOURCE
> Task :composeApp:prepareComposeResourcesTaskForAndroidDebug NO-SOURCE
> Task :composeApp:generateResourceAccessorsForAndroidDebug NO-SOURCE
> Task :composeApp:convertXmlValueResourcesForAndroidMain NO-SOURCE
> Task :composeApp:copyNonXmlValueResourcesForAndroidMain NO-SOURCE
> Task :composeApp:prepareComposeResourcesTaskForAndroidMain NO-SOURCE
> Task :composeApp:generateResourceAccessorsForAndroidMain NO-SOURCE
> Task :composeApp:convertXmlValueResourcesForCommonMain NO-SOURCE
> Task :composeApp:copyNonXmlValueResourcesForCommonMain NO-SOURCE
> Task :composeApp:prepareComposeResourcesTaskForCommonMain NO-SOURCE
> Task :composeApp:generateResourceAccessorsForCommonMain NO-SOURCE
> Task :core:file:preBuild UP-TO-DATE
> Task :core:file:preDebugBuild UP-TO-DATE
> Task :core:file:mergeDebugJniLibFolders
> Task :core:file:mergeDebugNativeLibs NO-SOURCE
> Task :core:file:stripDebugDebugSymbols NO-SOURCE
> Task :core:file:copyDebugJniLibsProjectAndLocalJars
> Task :core:file:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :core:file:generateDebugResValues FROM-CACHE
> Task :core:file:generateDebugResources FROM-CACHE
> Task :core:file:packageDebugResources
> Task :compose-ui:processDebugManifest
> Task :core:file:extractDeepLinksForAarDebug FROM-CACHE
> Task :core:file:javaPreCompileDebug FROM-CACHE
> Task :core:file:mergeDebugShaders
> Task :core:file:compileDebugShaders NO-SOURCE
> Task :core:file:debugAssetsCopyForAGP NO-SOURCE
> Task :core:file:generateDebugAssets UP-TO-DATE
> Task :core:file:packageDebugAssets
> Task :core:file:prepareDebugArtProfile
> Task :core:file:prepareLintJarForPublish
> Task :core:file:processDebugManifest
> Task :core:file:writeDebugAarMetadata
> Task :core:permissions:preBuild UP-TO-DATE
> Task :core:permissions:preDebugBuild UP-TO-DATE
> Task :core:permissions:mergeDebugJniLibFolders
> Task :core:permissions:mergeDebugNativeLibs NO-SOURCE
> Task :core:permissions:stripDebugDebugSymbols NO-SOURCE
> Task :core:permissions:copyDebugJniLibsProjectAndLocalJars
> Task :core:permissions:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :core:permissions:generateDebugResValues FROM-CACHE
> Task :core:permissions:generateDebugResources FROM-CACHE
> Task :core:permissions:packageDebugResources
> Task :core:file:parseDebugLocalResources
> Task :compose-ui:parseDebugLocalResources
> Task :core:permissions:parseDebugLocalResources
> Task :core:file:generateDebugRFile
> Task :core:permissions:generateDebugRFile
> Task :compose-ui:generateDebugRFile
> Task :core:permissions:extractDeepLinksForAarDebug FROM-CACHE
> Task :core:permissions:javaPreCompileDebug FROM-CACHE
> Task :core:permissions:mergeDebugShaders
> Task :core:permissions:compileDebugShaders NO-SOURCE
> Task :core:permissions:debugAssetsCopyForAGP NO-SOURCE
> Task :core:permissions:generateDebugAssets UP-TO-DATE
> Task :core:permissions:packageDebugAssets
> Task :core:permissions:prepareDebugArtProfile
> Task :core:permissions:prepareLintJarForPublish
> Task :core:permissions:processDebugManifest
> Task :core:permissions:writeDebugAarMetadata
> Task :core:presentation:preBuild UP-TO-DATE
> Task :core:presentation:preDebugBuild UP-TO-DATE
> Task :core:presentation:mergeDebugJniLibFolders
> Task :core:presentation:mergeDebugNativeLibs NO-SOURCE
> Task :core:presentation:stripDebugDebugSymbols NO-SOURCE
> Task :core:presentation:copyDebugJniLibsProjectAndLocalJars
> Task :core:presentation:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :core:presentation:convertXmlValueResourcesForAndroidMain NO-SOURCE
> Task :core:presentation:copyNonXmlValueResourcesForAndroidMain NO-SOURCE
> Task :core:presentation:prepareComposeResourcesTaskForAndroidMain NO-SOURCE
> Task :core:presentation:generateResourceAccessorsForAndroidMain NO-SOURCE
> Task :core:presentation:convertXmlValueResourcesForCommonMain NO-SOURCE
> Task :core:presentation:copyNonXmlValueResourcesForCommonMain NO-SOURCE
> Task :core:presentation:prepareComposeResourcesTaskForCommonMain NO-SOURCE
> Task :core:presentation:generateResourceAccessorsForCommonMain NO-SOURCE
> Task :core:presentation:generateActualResourceCollectorsForAndroidMain
> Task :core:presentation:generateComposeResClass
> Task :core:presentation:generateDebugResValues FROM-CACHE
> Task :core:presentation:generateDebugResources FROM-CACHE
> Task :core:presentation:packageDebugResources
> Task :core:presentation:parseDebugLocalResources FROM-CACHE
> Task :core:presentation:generateDebugRFile
> Task :core:presentation:generateExpectResourceCollectorsForCommonMain
> Task :core:presentation:convertXmlValueResourcesForAndroidDebug NO-SOURCE
> Task :core:presentation:copyNonXmlValueResourcesForAndroidDebug NO-SOURCE
> Task :core:presentation:prepareComposeResourcesTaskForAndroidDebug NO-SOURCE
> Task :core:presentation:generateResourceAccessorsForAndroidDebug NO-SOURCE
> Task :core:datetime:compileKotlinJvm
> Task :core:datetime:compileJvmMainJava NO-SOURCE
> Task :core:datetime:jvmMainClasses
> Task :core:datetime:jvmJar
> Task :core:permissions:compileDebugKotlinAndroid
> Task :core:file:compileDebugKotlinAndroid

> Task :core:presentation:compileDebugKotlinAndroid
w: file:///opt/atlassian/pipelines/agent/build/core/presentation/src/androidMain/kotlin/com/quadhub/core/presentation/navigation/MyParcelize.android.kt:5:1 'expect'/'actual' classes (including interfaces, objects, annotations, enums, and 'actual' typealiases) are in Beta. Consider using the '-Xexpect-actual-classes' flag to suppress this warning. Also see: https://youtrack.jetbrains.com/issue/KT-61573
w: file:///opt/atlassian/pipelines/agent/build/core/presentation/src/androidMain/kotlin/com/quadhub/core/presentation/navigation/MyParcelize.android.kt:7:1 'expect'/'actual' classes (including interfaces, objects, annotations, enums, and 'actual' typealiases) are in Beta. Consider using the '-Xexpect-actual-classes' flag to suppress this warning. Also see: https://youtrack.jetbrains.com/issue/KT-61573
w: file:///opt/atlassian/pipelines/agent/build/core/presentation/src/commonMain/kotlin/com/quadhub/core/presentation/navigation/MyParcelize.kt:8:1 'expect'/'actual' classes (including interfaces, objects, annotations, enums, and 'actual' typealiases) are in Beta. Consider using the '-Xexpect-actual-classes' flag to suppress this warning. Also see: https://youtrack.jetbrains.com/issue/KT-61573
w: file:///opt/atlassian/pipelines/agent/build/core/presentation/src/commonMain/kotlin/com/quadhub/core/presentation/navigation/MyParcelize.kt:13:1 'expect'/'actual' classes (including interfaces, objects, annotations, enums, and 'actual' typealiases) are in Beta. Consider using the '-Xexpect-actual-classes' flag to suppress this warning. Also see: https://youtrack.jetbrains.com/issue/KT-61573

> Task :core:file:compileDebugJavaWithJavac NO-SOURCE
> Task :core:file:mergeDebugGeneratedProguardFiles
> Task :core:file:mergeDebugConsumerProguardFiles
> Task :core:file:processDebugJavaRes
> Task :core:file:mergeDebugJavaResource
> Task :core:permissions:compileDebugJavaWithJavac NO-SOURCE
> Task :core:permissions:mergeDebugGeneratedProguardFiles
> Task :core:permissions:mergeDebugConsumerProguardFiles
> Task :core:permissions:processDebugJavaRes
> Task :core:permissions:mergeDebugJavaResource
> Task :core:permissions:extractDebugAnnotations
> Task :core:file:extractDebugAnnotations
> Task :core:permissions:syncDebugLibJars
> Task :core:file:syncDebugLibJars
> Task :core:permissions:bundleDebugLocalLintAar
> Task :core:file:bundleDebugLocalLintAar
> Task :core:presentation:extractDeepLinksForAarDebug FROM-CACHE
> Task :core:presentation:javaPreCompileDebug FROM-CACHE
> Task :core:presentation:compileDebugJavaWithJavac NO-SOURCE
> Task :core:presentation:mergeDebugGeneratedProguardFiles
> Task :core:presentation:mergeDebugShaders
> Task :core:presentation:mergeDebugConsumerProguardFiles
> Task :core:presentation:compileDebugShaders NO-SOURCE
> Task :core:presentation:debugAssetsCopyForAGP NO-SOURCE
> Task :core:presentation:generateDebugAssets UP-TO-DATE
> Task :core:presentation:copyDebugComposeResourcesToAndroidAssets
> Task :core:presentation:prepareDebugArtProfile
> Task :core:presentation:prepareLintJarForPublish
> Task :core:presentation:packageDebugAssets
> Task :core:presentation:processDebugJavaRes
> Task :core:presentation:processDebugManifest
> Task :core:presentation:mergeDebugJavaResource
> Task :core:recorder:preBuild UP-TO-DATE
> Task :core:recorder:preDebugBuild UP-TO-DATE
> Task :core:presentation:writeDebugAarMetadata
> Task :core:recorder:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :core:recorder:generateDebugResValues FROM-CACHE
> Task :core:recorder:generateDebugResources FROM-CACHE
> Task :core:recorder:mergeDebugJniLibFolders
> Task :core:recorder:mergeDebugNativeLibs NO-SOURCE
> Task :core:recorder:stripDebugDebugSymbols NO-SOURCE
> Task :core:recorder:copyDebugJniLibsProjectAndLocalJars
> Task :core:recorder:extractDeepLinksForAarDebug FROM-CACHE
> Task :core:recorder:javaPreCompileDebug FROM-CACHE
> Task :core:recorder:packageDebugResources
> Task :core:recorder:mergeDebugShaders
> Task :core:recorder:compileDebugShaders NO-SOURCE
> Task :core:recorder:debugAssetsCopyForAGP NO-SOURCE
> Task :core:recorder:parseDebugLocalResources FROM-CACHE
> Task :core:recorder:generateDebugAssets UP-TO-DATE
> Task :core:recorder:generateDebugRFile
> Task :core:recorder:packageDebugAssets
> Task :core:recorder:prepareDebugArtProfile
> Task :core:recorder:prepareLintJarForPublish
> Task :core:recorder:processDebugManifest
> Task :core:recorder:writeDebugAarMetadata
> Task :features:auth:ui:preBuild UP-TO-DATE
> Task :features:auth:ui:preDebugBuild UP-TO-DATE
> Task :features:auth:ui:mergeDebugJniLibFolders
> Task :features:auth:ui:mergeDebugNativeLibs NO-SOURCE
> Task :features:auth:ui:stripDebugDebugSymbols NO-SOURCE
> Task :core:presentation:extractDebugAnnotations
> Task :core:presentation:syncDebugLibJars
> Task :core:presentation:bundleDebugLocalLintAar
> Task :core:discopes:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :features:auth:ui:copyDebugJniLibsProjectAndLocalJars
> Task :core:discopes:kspKotlinJvm SKIPPED
> Task :core:discopes:jvmProcessResources NO-SOURCE
> Task :core:discopes:processJvmMainResources SKIPPED
> Task :features:auth:usecase:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :core:discopes:generateProjectStructureMetadata
> Task :core:discopes:metadataCommonMainProcessResources NO-SOURCE
> Task :core:discopes:metadataNativeMainProcessResources NO-SOURCE
> Task :core:discopes:metadataAppleMainProcessResources NO-SOURCE
> Task :core:discopes:metadataIosMainProcessResources NO-SOURCE
> Task :core:discopes:transformCommonMainDependenciesMetadata
> Task :core:presentation:bundleLibCompileToJarDebug
> Task :features:auth:ui:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :features:auth:ui:convertXmlValueResourcesForAndroidMain NO-SOURCE
> Task :features:auth:ui:copyNonXmlValueResourcesForAndroidMain NO-SOURCE
> Task :features:auth:ui:prepareComposeResourcesTaskForAndroidMain NO-SOURCE
> Task :features:auth:ui:generateResourceAccessorsForAndroidMain NO-SOURCE
> Task :features:auth:ui:convertXmlValueResourcesForCommonMain NO-SOURCE
> Task :features:auth:ui:copyNonXmlValueResourcesForCommonMain NO-SOURCE
> Task :features:auth:ui:prepareComposeResourcesTaskForCommonMain NO-SOURCE
> Task :features:auth:ui:generateResourceAccessorsForCommonMain NO-SOURCE
> Task :features:auth:ui:generateActualResourceCollectorsForAndroidMain
> Task :features:auth:ui:generateComposeResClass
> Task :features:auth:ui:generateDebugResValues FROM-CACHE
> Task :features:auth:ui:generateDebugResources FROM-CACHE
> Task :features:auth:ui:packageDebugResources
> Task :features:auth:ui:parseDebugLocalResources FROM-CACHE
> Task :features:auth:ui:generateDebugRFile
> Task :features:auth:ui:generateExpectResourceCollectorsForCommonMain
> Task :features:auth:ui:convertXmlValueResourcesForAndroidDebug NO-SOURCE
> Task :features:auth:ui:copyNonXmlValueResourcesForAndroidDebug NO-SOURCE
> Task :features:auth:ui:prepareComposeResourcesTaskForAndroidDebug NO-SOURCE
> Task :features:auth:ui:generateResourceAccessorsForAndroidDebug NO-SOURCE
> Task :core:datetime:transformCommonMainDependenciesMetadata
> Task :core:datetime:compileCommonMainKotlinMetadata
> Task :core:datetime:generateProjectStructureMetadata
> Task :core:datetime:metadataCommonMainProcessResources NO-SOURCE
> Task :core:datetime:metadataCommonMainClasses
> Task :core:datetime:allMetadataJar
> Task :core:datetime:compileNativeMainKotlinMetadata SKIPPED
> Task :core:datetime:metadataNativeMainProcessResources NO-SOURCE
> Task :core:datetime:metadataNativeMainClasses UP-TO-DATE
> Task :core:datetime:compileAppleMainKotlinMetadata SKIPPED
> Task :core:datetime:metadataAppleMainProcessResources NO-SOURCE
> Task :core:datetime:metadataAppleMainClasses UP-TO-DATE
> Task :core:datetime:compileIosMainKotlinMetadata SKIPPED
> Task :core:datetime:metadataIosMainProcessResources NO-SOURCE
> Task :core:datetime:metadataIosMainClasses UP-TO-DATE
> Task :core:datetime:exportCommonSourceSetsMetadataLocationsForMetadataApiElements
> Task :compose-ui:transformCommonMainDependenciesMetadata

> Task :core:recorder:compileDebugKotlinAndroid
w: file:///opt/atlassian/pipelines/agent/build/core/recorder/src/androidMain/kotlin/com/quadhub/core/record/AudioRecorderManagerImpl.kt:62:94 'constructor(): MediaRecorder' is deprecated. Deprecated in Java.

> Task :core:recorder:extractDebugAnnotations
> Task :core:recorder:compileDebugJavaWithJavac NO-SOURCE
> Task :core:recorder:mergeDebugGeneratedProguardFiles
> Task :core:recorder:mergeDebugConsumerProguardFiles
> Task :core:recorder:processDebugJavaRes
> Task :core:recorder:mergeDebugJavaResource
> Task :core:recorder:syncDebugLibJars
> Task :core:recorder:bundleDebugLocalLintAar
> Task :core:datetime:exportRootPublicationCoordinatesForMetadataApiElements
> Task :compose-ui:generateProjectStructureMetadata
> Task :compose-ui:metadataCommonMainProcessResources NO-SOURCE
> Task :core:presentation:transformCommonMainDependenciesMetadata

> Task :core:presentation:compileCommonMainKotlinMetadata
w: file:///opt/atlassian/pipelines/agent/build/core/presentation/src/commonMain/kotlin/com/quadhub/core/presentation/navigation/MyParcelize.kt:8:1 'expect'/'actual' classes (including interfaces, objects, annotations, enums, and 'actual' typealiases) are in Beta. Consider using the '-Xexpect-actual-classes' flag to suppress this warning. Also see: https://youtrack.jetbrains.com/issue/KT-61573
w: file:///opt/atlassian/pipelines/agent/build/core/presentation/src/commonMain/kotlin/com/quadhub/core/presentation/navigation/MyParcelize.kt:13:1 'expect'/'actual' classes (including interfaces, objects, annotations, enums, and 'actual' typealiases) are in Beta. Consider using the '-Xexpect-actual-classes' flag to suppress this warning. Also see: https://youtrack.jetbrains.com/issue/KT-61573

> Task :core:presentation:generateProjectStructureMetadata
> Task :core:presentation:metadataCommonMainProcessResources NO-SOURCE
> Task :core:presentation:metadataCommonMainClasses
> Task :core:presentation:allMetadataJar
> Task :compose-ui:metadataNativeMainProcessResources NO-SOURCE
> Task :compose-ui:metadataAppleMainProcessResources NO-SOURCE
> Task :compose-ui:metadataIosMainProcessResources NO-SOURCE
> Task :core:presentation:convertXmlValueResourcesForNativeMain NO-SOURCE
> Task :core:presentation:copyNonXmlValueResourcesForNativeMain NO-SOURCE
> Task :core:presentation:prepareComposeResourcesTaskForNativeMain NO-SOURCE
> Task :core:presentation:generateResourceAccessorsForNativeMain NO-SOURCE
> Task :core:presentation:compileNativeMainKotlinMetadata SKIPPED
> Task :core:presentation:convertXmlValueResourcesForAppleMain NO-SOURCE
> Task :core:presentation:copyNonXmlValueResourcesForAppleMain NO-SOURCE
> Task :core:presentation:prepareComposeResourcesTaskForAppleMain NO-SOURCE
> Task :core:presentation:generateResourceAccessorsForAppleMain NO-SOURCE
> Task :core:presentation:metadataNativeMainProcessResources NO-SOURCE
> Task :core:presentation:metadataNativeMainClasses UP-TO-DATE
> Task :core:presentation:compileAppleMainKotlinMetadata SKIPPED
> Task :core:presentation:convertXmlValueResourcesForIosMain NO-SOURCE
> Task :core:presentation:copyNonXmlValueResourcesForIosMain NO-SOURCE
> Task :core:presentation:prepareComposeResourcesTaskForIosMain NO-SOURCE
> Task :core:presentation:generateResourceAccessorsForIosMain NO-SOURCE
> Task :core:presentation:metadataAppleMainProcessResources NO-SOURCE
> Task :core:presentation:metadataAppleMainClasses UP-TO-DATE
> Task :core:presentation:compileIosMainKotlinMetadata SKIPPED
> Task :core:presentation:metadataIosMainProcessResources NO-SOURCE
> Task :core:presentation:metadataIosMainClasses UP-TO-DATE
> Task :core:presentation:exportCommonSourceSetsMetadataLocationsForMetadataApiElements
> Task :core:discopes:exportRootPublicationCoordinatesForMetadataApiElements
> Task :features:auth:usecase:generateProjectStructureMetadata
> Task :features:auth:usecase:metadataCommonMainProcessResources NO-SOURCE
> Task :features:auth:usecase:metadataNativeMainProcessResources NO-SOURCE
> Task :features:auth:usecase:metadataAppleMainProcessResources NO-SOURCE
> Task :features:auth:usecase:metadataIosMainProcessResources NO-SOURCE
> Task :features:auth:ui:extractDeepLinksForAarDebug FROM-CACHE
> Task :features:auth:ui:javaPreCompileDebug FROM-CACHE
> Task :features:auth:ui:mergeDebugShaders
> Task :features:auth:ui:compileDebugShaders NO-SOURCE
> Task :features:auth:ui:copyDebugComposeResourcesToAndroidAssets
> Task :features:auth:ui:debugAssetsCopyForAGP NO-SOURCE
> Task :features:auth:ui:generateDebugAssets UP-TO-DATE
> Task :features:auth:ui:packageDebugAssets
> Task :features:auth:ui:prepareDebugArtProfile
> Task :features:auth:ui:prepareLintJarForPublish
> Task :features:auth:ui:processDebugManifest
> Task :features:auth:ui:writeDebugAarMetadata
> Task :core:auth:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :core:sharedpref:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :compose-ui:compileCommonMainKotlinMetadata
> Task :compose-ui:metadataCommonMainClasses
> Task :compose-ui:allMetadataJar
> Task :compose-ui:compileNativeMainKotlinMetadata SKIPPED
> Task :compose-ui:metadataNativeMainClasses UP-TO-DATE
> Task :compose-ui:compileAppleMainKotlinMetadata SKIPPED
> Task :compose-ui:metadataAppleMainClasses UP-TO-DATE
> Task :compose-ui:compileIosMainKotlinMetadata SKIPPED
> Task :compose-ui:metadataIosMainClasses UP-TO-DATE
> Task :compose-ui:exportCommonSourceSetsMetadataLocationsForMetadataApiElements
> Task :core:sharedpref:generateProjectStructureMetadata
> Task :core:sharedpref:metadataCommonMainProcessResources NO-SOURCE
> Task :core:sharedpref:metadataNativeMainProcessResources NO-SOURCE
> Task :core:sharedpref:metadataAppleMainProcessResources NO-SOURCE
> Task :core:sharedpref:metadataIosMainProcessResources NO-SOURCE
> Task :core:sharedpref:transformCommonMainDependenciesMetadata
> Task :core:discopes:kspCommonMainKotlinMetadata
> Task :core:sharedpref:compileCommonMainKotlinMetadata
> Task :core:discopes:compileKotlinJvm
> Task :core:discopes:compileJvmMainJava NO-SOURCE
> Task :core:discopes:jvmMainClasses
> Task :core:discopes:compileCommonMainKotlinMetadata
> Task :core:discopes:metadataCommonMainClasses
> Task :core:discopes:allMetadataJar
> Task :core:discopes:compileNativeMainKotlinMetadata SKIPPED
> Task :core:discopes:metadataNativeMainClasses UP-TO-DATE
> Task :core:discopes:compileAppleMainKotlinMetadata SKIPPED
> Task :core:discopes:metadataAppleMainClasses UP-TO-DATE
> Task :core:discopes:compileIosMainKotlinMetadata SKIPPED
> Task :core:discopes:metadataIosMainClasses UP-TO-DATE
> Task :core:discopes:jvmJar
> Task :core:discopes:exportCommonSourceSetsMetadataLocationsForMetadataApiElements
> Task :features:auth:usecase:kspKotlinJvm SKIPPED
> Task :features:auth:usecase:jvmProcessResources NO-SOURCE
> Task :features:auth:usecase:processJvmMainResources SKIPPED
> Task :core:sharedpref:metadataCommonMainClasses
> Task :core:sharedpref:allMetadataJar
> Task :core:sharedpref:compileNativeMainKotlinMetadata SKIPPED
> Task :core:sharedpref:metadataNativeMainClasses UP-TO-DATE
> Task :core:sharedpref:compileAppleMainKotlinMetadata SKIPPED
> Task :core:sharedpref:metadataAppleMainClasses UP-TO-DATE
> Task :core:sharedpref:compileIosMainKotlinMetadata SKIPPED
> Task :core:sharedpref:metadataIosMainClasses UP-TO-DATE
> Task :core:sharedpref:exportCommonSourceSetsMetadataLocationsForMetadataApiElements
> Task :core:sharedpref:compileKotlinJvm
> Task :core:sharedpref:compileJvmMainJava NO-SOURCE
> Task :core:sharedpref:jvmProcessResources NO-SOURCE
> Task :core:sharedpref:processJvmMainResources SKIPPED
> Task :core:sharedpref:jvmMainClasses
> Task :core:sharedpref:jvmJar
> Task :core:auth:kspKotlinJvm SKIPPED
> Task :core:auth:jvmProcessResources NO-SOURCE
> Task :core:auth:processJvmMainResources SKIPPED
> Task :core:log:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :core:log:kspKotlinJvm SKIPPED
> Task :features:auth:usecase:transformCommonMainDependenciesMetadata
> Task :features:auth:usecase:kspCommonMainKotlinMetadata NO-SOURCE
> Task :features:auth:usecase:compileKotlinJvm NO-SOURCE
> Task :features:auth:usecase:compileJvmMainJava NO-SOURCE
> Task :features:auth:usecase:jvmMainClasses UP-TO-DATE
> Task :features:auth:usecase:jvmJar
> Task :features:auth:usecase:compileCommonMainKotlinMetadata NO-SOURCE
> Task :features:auth:usecase:metadataCommonMainClasses UP-TO-DATE
> Task :features:auth:usecase:allMetadataJar
> Task :features:auth:usecase:compileNativeMainKotlinMetadata SKIPPED
> Task :features:auth:usecase:metadataNativeMainClasses UP-TO-DATE
> Task :features:auth:usecase:compileAppleMainKotlinMetadata SKIPPED
> Task :features:auth:usecase:metadataAppleMainClasses UP-TO-DATE
> Task :features:auth:usecase:compileIosMainKotlinMetadata SKIPPED
> Task :features:auth:usecase:metadataIosMainClasses UP-TO-DATE
> Task :features:auth:usecase:exportCommonSourceSetsMetadataLocationsForMetadataApiElements
> Task :core:auth:transformCommonMainDependenciesMetadata
> Task :compose-ui:compileDebugKotlinAndroid
> Task :core:log:compileKotlinJvm
> Task :compose-ui:compileDebugJavaWithJavac NO-SOURCE
> Task :compose-ui:mergeDebugGeneratedProguardFiles
> Task :compose-ui:mergeDebugConsumerProguardFiles
> Task :compose-ui:processDebugJavaRes
> Task :compose-ui:mergeDebugJavaResource
> Task :compose-ui:bundleLibRuntimeToJarDebug
> Task :compose-ui:createFullJarDebug
> Task :compose-ui:extractDebugAnnotations
> Task :compose-ui:syncDebugLibJars
> Task :compose-ui:bundleDebugLocalLintAar
> Task :features:auth:ui:transformCommonMainDependenciesMetadata
> Task :core:auth:kspCommonMainKotlinMetadata
> Task :compose-ui:generateDebugUnitTestLintModel
> Task :compose-ui:generateDebugLintReportModel
> Task :compose-ui:generateDebugAndroidTestLintModel
> Task :compose-ui:generateDebugLintModel
> Task :compose-ui:bundleLibCompileToJarDebug
> Task :features:auth:ui:kspCommonMainKotlinMetadata
> Task :features:auth:ui:kspDebugKotlinAndroid SKIPPED
> Task :features:auth:ui:compileDebugKotlinAndroid
> Task :features:auth:ui:extractDebugAnnotations
> Task :features:auth:ui:compileDebugJavaWithJavac NO-SOURCE
> Task :features:auth:ui:mergeDebugGeneratedProguardFiles
> Task :features:auth:ui:mergeDebugConsumerProguardFiles
> Task :features:auth:ui:processDebugJavaRes
> Task :features:auth:ui:mergeDebugJavaResource
> Task :features:auth:ui:syncDebugLibJars
> Task :features:auth:ui:bundleDebugLocalLintAar
> Task :core:auth:compileKotlinJvm
> Task :core:auth:compileJvmMainJava NO-SOURCE
> Task :core:auth:jvmMainClasses
> Task :core:auth:jvmJar
> Task :core:log:compileJvmMainJava NO-SOURCE
> Task :core:log:jvmProcessResources NO-SOURCE
> Task :core:log:processJvmMainResources SKIPPED
> Task :core:log:jvmMainClasses
> Task :core:log:jvmJar
> Task :core:network:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :core:network:kspKotlinJvm SKIPPED
> Task :core:network:compileKotlinJvm
> Task :core:network:compileJvmMainJava NO-SOURCE
> Task :core:network:jvmProcessResources NO-SOURCE
> Task :core:network:processJvmMainResources SKIPPED
> Task :core:network:jvmMainClasses
> Task :core:network:jvmJar
> Task :composeApp:checkKotlinGradlePluginConfigurationErrors SKIPPED
> Task :composeApp:generateActualResourceCollectorsForAndroidMain
> Task :composeApp:generateComposeResClass
> Task :composeApp:generateExpectResourceCollectorsForCommonMain
> Task :core:auth:compileCommonMainKotlinMetadata
> Task :core:sharedpref:exportRootPublicationCoordinatesForMetadataApiElements
> Task :core:auth:generateProjectStructureMetadata
> Task :core:auth:metadataCommonMainProcessResources NO-SOURCE
> Task :core:auth:metadataCommonMainClasses
> Task :core:auth:allMetadataJar
> Task :core:auth:compileNativeMainKotlinMetadata SKIPPED
> Task :core:auth:metadataNativeMainProcessResources NO-SOURCE
> Task :core:auth:metadataNativeMainClasses UP-TO-DATE
> Task :core:auth:compileAppleMainKotlinMetadata SKIPPED
> Task :core:auth:metadataAppleMainProcessResources NO-SOURCE
> Task :core:auth:metadataAppleMainClasses UP-TO-DATE
> Task :core:auth:compileIosMainKotlinMetadata SKIPPED
> Task :core:auth:metadataIosMainProcessResources NO-SOURCE
> Task :core:auth:metadataIosMainClasses UP-TO-DATE
> Task :core:auth:exportCommonSourceSetsMetadataLocationsForMetadataApiElements
> Task :core:file:transformCommonMainDependenciesMetadata
> Task :core:file:compileCommonMainKotlinMetadata
> Task :core:file:generateProjectStructureMetadata
> Task :core:file:metadataCommonMainProcessResources NO-SOURCE
> Task :core:file:metadataCommonMainClasses
> Task :core:file:allMetadataJar
> Task :core:file:compileNativeMainKotlinMetadata SKIPPED
> Task :core:file:metadataNativeMainProcessResources NO-SOURCE
> Task :core:file:metadataNativeMainClasses UP-TO-DATE
> Task :core:file:compileAppleMainKotlinMetadata SKIPPED
> Task :core:file:metadataAppleMainProcessResources NO-SOURCE
> Task :core:file:metadataAppleMainClasses UP-TO-DATE
> Task :core:file:compileIosMainKotlinMetadata SKIPPED
> Task :core:file:metadataIosMainProcessResources NO-SOURCE
> Task :core:file:metadataIosMainClasses UP-TO-DATE
> Task :core:file:exportCommonSourceSetsMetadataLocationsForMetadataApiElements
> Task :core:log:transformCommonMainDependenciesMetadata
> Task :core:log:kspCommonMainKotlinMetadata
> Task :core:log:compileCommonMainKotlinMetadata
> Task :core:log:generateProjectStructureMetadata
> Task :core:log:metadataCommonMainProcessResources NO-SOURCE
> Task :core:log:metadataCommonMainClasses
> Task :core:log:allMetadataJar
> Task :core:log:compileNativeMainKotlinMetadata SKIPPED
> Task :core:log:metadataNativeMainProcessResources NO-SOURCE
> Task :core:log:metadataNativeMainClasses UP-TO-DATE
> Task :core:log:compileAppleMainKotlinMetadata SKIPPED
> Task :core:log:metadataAppleMainProcessResources NO-SOURCE
> Task :core:log:metadataAppleMainClasses UP-TO-DATE
> Task :core:log:compileIosMainKotlinMetadata SKIPPED
> Task :core:log:metadataIosMainProcessResources NO-SOURCE
> Task :core:log:metadataIosMainClasses UP-TO-DATE
> Task :core:log:exportCommonSourceSetsMetadataLocationsForMetadataApiElements
> Task :core:network:transformCommonMainDependenciesMetadata
> Task :core:network:kspCommonMainKotlinMetadata
> Task :core:network:compileCommonMainKotlinMetadata
> Task :core:auth:exportRootPublicationCoordinatesForMetadataApiElements
> Task :core:log:exportRootPublicationCoordinatesForMetadataApiElements
> Task :core:network:generateProjectStructureMetadata
> Task :core:network:metadataCommonMainProcessResources NO-SOURCE
> Task :core:network:metadataCommonMainClasses
> Task :core:network:allMetadataJar
> Task :core:network:compileNativeMainKotlinMetadata SKIPPED
> Task :core:network:metadataNativeMainProcessResources NO-SOURCE
> Task :core:network:metadataNativeMainClasses UP-TO-DATE
> Task :core:network:compileAppleMainKotlinMetadata SKIPPED
> Task :core:network:metadataAppleMainProcessResources NO-SOURCE
> Task :core:network:metadataAppleMainClasses UP-TO-DATE
> Task :core:network:compileIosMainKotlinMetadata SKIPPED
> Task :core:network:metadataIosMainProcessResources NO-SOURCE
> Task :core:network:metadataIosMainClasses UP-TO-DATE
> Task :core:network:exportCommonSourceSetsMetadataLocationsForMetadataApiElements
> Task :core:permissions:transformCommonMainDependenciesMetadata
> Task :core:permissions:compileCommonMainKotlinMetadata
> Task :core:permissions:generateProjectStructureMetadata
> Task :core:permissions:metadataCommonMainProcessResources NO-SOURCE
> Task :core:permissions:metadataCommonMainClasses
> Task :core:permissions:allMetadataJar
> Task :core:permissions:compileNativeMainKotlinMetadata SKIPPED
> Task :core:permissions:metadataNativeMainProcessResources NO-SOURCE
> Task :core:permissions:metadataNativeMainClasses UP-TO-DATE
> Task :core:permissions:compileAppleMainKotlinMetadata SKIPPED
> Task :core:permissions:metadataAppleMainProcessResources NO-SOURCE
> Task :core:permissions:metadataAppleMainClasses UP-TO-DATE
> Task :core:permissions:compileIosMainKotlinMetadata SKIPPED
> Task :core:permissions:metadataIosMainProcessResources NO-SOURCE
> Task :core:permissions:metadataIosMainClasses UP-TO-DATE
> Task :core:permissions:exportCommonSourceSetsMetadataLocationsForMetadataApiElements
> Task :core:recorder:transformCommonMainDependenciesMetadata
> Task :core:recorder:compileCommonMainKotlinMetadata
> Task :core:recorder:generateProjectStructureMetadata
> Task :core:recorder:metadataCommonMainProcessResources NO-SOURCE
> Task :core:recorder:metadataCommonMainClasses
> Task :core:recorder:allMetadataJar
> Task :core:recorder:compileNativeMainKotlinMetadata SKIPPED
> Task :core:recorder:metadataNativeMainProcessResources NO-SOURCE
> Task :core:recorder:metadataNativeMainClasses UP-TO-DATE
> Task :core:recorder:compileAppleMainKotlinMetadata SKIPPED
> Task :core:recorder:metadataAppleMainProcessResources NO-SOURCE
> Task :core:recorder:metadataAppleMainClasses UP-TO-DATE
> Task :core:recorder:compileIosMainKotlinMetadata SKIPPED
> Task :core:recorder:metadataIosMainProcessResources NO-SOURCE
> Task :core:recorder:metadataIosMainClasses UP-TO-DATE
> Task :core:recorder:exportCommonSourceSetsMetadataLocationsForMetadataApiElements
> Task :compose-ui:lintAnalyzeDebug FAILED
> Task :features:auth:ui:compileCommonMainKotlinMetadata

FAILURE: Build failed with an exception.

* What went wrong:
> Task :compose-ui:lintAnalyzeDebugAndroidTest
> Task :compose-ui:lintAnalyzeDebugUnitTest
Execution failed for task ':compose-ui:lintAnalyzeDebug'.
> A failure occurred while executing com.android.build.gradle.internal.lint.AndroidLintWorkAction
   > Unexpected failure during lint analysis (this is a bug in lint or one of the libraries it depends on)
     
     Message: Unexpected failure during lint analysis (this is a bug in lint or one of the libraries it depends on)
     
     Message: Unexpected failure during lint analysis of CoreBackHandler.android.kt (this is a bug in lint or one of the libraries it depends on)
     
     Message: Found class org.jetbrains.kotlin.analysis.api.resolution.KaCallableMemberCall, but interface was expected
     
     The crash seems to involve the detector \\\`androidx.lifecycle.lint.NonNullableMutableLiveDataDetector\\\`.
     You can try disabling it with something like this:
         android {
             lint {
                 disable "NullSafeMutableLiveData"
             }
         }
     
     Stack: \\\`IncompatibleClassChangeError:NonNullableMutableLiveDataDetector$createUastHandler$1.visitCallExpression(NonNullableMutableLiveDataDetector.kt:140)←UElementVisitor$DispatchUastVisitor.visitCallExpression(UElementVisitor.kt:412)←UElementVisitor$DelegatingUastVisitor.visitCallExpression(UElementVisitor.kt:756)←UCallExpression.accept(UCallExpression.kt:94)←ImplementationUtilsKt.acceptList(implementationUtils.kt:15)←UBlockExpression.accept(UBlockExpression.kt:21)←UMethod.accept(UMethod.kt:45)←ImplementationUtilsKt.acceptList(implementationUtils.kt:15)←AbstractKotlinUClass.accept(AbstractKotlinUClass.kt:213)←ImplementationUtilsKt.acceptList(implementationUtils.kt:15)←UFile.accept(UFile.kt:89)←UastLintUtilsKt.acceptSourceFile(UastLintUtils.kt:953)←UElementVisitor$visitFile$3.run(UElementVisitor.kt:216)←LintClient.runReadAction(LintClient.kt:1781)←LintDriver$LintClientWrapper.runReadAction(LintDriver.kt:2747)←UElementVisitor.visitFile(UElementVisitor.kt:213)←LintDriver$visitUastDetectors$1.run(LintDriver.kt:2048)←LintClient.runReadAction(LintClient.kt:1781)←LintDriver$LintClientWrapper.runReadAction(LintDriver.kt:2747)←LintDriver.visitUastDetectors(LintDriver.kt:2048)←LintDriver.visitUast(LintDriver.kt:2010)←LintDriver.runFileDetectors(LintDriver.kt:1284)←LintDriver.checkProject(LintDriver.kt:1070)←LintDriver.checkProjectRoot(LintDriver.kt:622)←LintDriver.access$checkProjectRoot(LintDriver.kt:174)←LintDriver$analyzeOnly$1.invoke(LintDriver.kt:445)←LintDriver$analyzeOnly$1.invoke(LintDriver.kt:442)←LintDriver.doAnalyze(LintDriver.kt:501)←LintDriver.analyzeOnly(LintDriver.kt:442)←LintCliClient$analyzeOnly$1.invoke(LintCliClient.kt:258)←LintCliClient$analyzeOnly$1.invoke(LintCliClient.kt:255)←LintCliClient.run(LintCliClient.kt:312)←LintCliClient.analyzeOnly(LintCliClient.kt:255)←Main.run(Main.java:1766)←Main.run(Main.java:282)←DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)←Method.invoke(Method.java:580)←AndroidLintWorkAction.invokeLintMainRunMethod(AndroidLintWorkAction.kt:103)←AndroidLintWorkAction.runLint(AndroidLintWorkAction.kt:90)←AndroidLintWorkAction.execute(AndroidLintWorkAction.kt:64)←DefaultWorkerServer.execute(DefaultWorkerServer.java:63)←NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:66)←NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:62)←ClassLoaderUtils.executeInClassloader(ClassLoaderUtils.java:100)←NoIsolationWorkerFactory$1.lambda$execute$0(NoIsolationWorkerFactory.java:62)←AbstractWorker$1.call(AbstractWorker.java:44)←AbstractWorker$1.call(AbstractWorker.java:41)←DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:209)←DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)←DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)←DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)←DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)←DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)←DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)←AbstractWorker.executeWrappedInBuildOperation(AbstractWorker.java:41)←NoIsolationWorkerFactory$1.execute(NoIsolationWorkerFactory.java:59)←DefaultWorkerExecutor.lambda$submitWork$0(DefaultWorkerExecutor.java:174)←FutureTask.run(FutureTask.java:317)←DefaultConditionalExecutionQueue$ExecutionRunner.runExecution(DefaultConditionalExecutionQueue.java:195)←DefaultConditionalExecutionQueue$ExecutionRunner.access$700(DefaultConditionalExecutionQueue.java:128)←DefaultConditionalExecutionQueue$ExecutionRunner$1.run(DefaultConditionalExecutionQueue.java:170)←Factories$1.create(Factories.java:31)←DefaultWorkerLeaseService.withLocks(DefaultWorkerLeaseService.java:267)←DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:131)←DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:136)←DefaultConditionalExecutionQueue$ExecutionRunner.runBatch(DefaultConditionalExecutionQueue.java:165)←DefaultConditionalExecutionQueue$ExecutionRunner.run(DefaultConditionalExecutionQueue.java:134)←Executors$RunnableAdapter.call(Executors.java:572)←FutureTask.run(FutureTask.java:317)←ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)←AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:48)←ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)←ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)←Thread.run(Thread.java:1583)\\\`
     Stack: \`RuntimeException:LintDriver$Companion.handleDetectorError(LintDriver.kt:3740)←LintDriver$Companion.handleDetectorError$default(LintDriver.kt:3607)←LintDriver$Companion.handleDetectorError(LintDriver.kt:3603)←UElementVisitor.visitFile(UElementVisitor.kt:242)←LintDriver$visitUastDetectors$1.run(LintDriver.kt:2048)←LintClient.runReadAction(LintClient.kt:1781)←LintDriver$LintClientWrapper.runReadAction(LintDriver.kt:2747)←LintDriver.visitUastDetectors(LintDriver.kt:2048)←LintDriver.visitUast(LintDriver.kt:2010)←LintDriver.runFileDetectors(LintDriver.kt:1284)←LintDriver.checkProject(LintDriver.kt:1070)←LintDriver.checkProjectRoot(LintDriver.kt:622)←LintDriver.access$checkProjectRoot(LintDriver.kt:174)←LintDriver$analyzeOnly$1.invoke(LintDriver.kt:445)←LintDriver$analyzeOnly$1.invoke(LintDriver.kt:442)←LintDriver.doAnalyze(LintDriver.kt:501)←LintDriver.analyzeOnly(LintDriver.kt:442)←LintCliClient$analyzeOnly$1.invoke(LintCliClient.kt:258)←LintCliClient$analyzeOnly$1.invoke(LintCliClient.kt:255)←LintCliClient.run(LintCliClient.kt:312)←LintCliClient.analyzeOnly(LintCliClient.kt:255)←Main.run(Main.java:1766)←Main.run(Main.java:282)←DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)←Method.invoke(Method.java:580)←AndroidLintWorkAction.invokeLintMainRunMethod(AndroidLintWorkAction.kt:103)←AndroidLintWorkAction.runLint(AndroidLintWorkAction.kt:90)←AndroidLintWorkAction.execute(AndroidLintWorkAction.kt:64)←DefaultWorkerServer.execute(DefaultWorkerServer.java:63)←NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:66)←NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:62)←ClassLoaderUtils.executeInClassloader(ClassLoaderUtils.java:100)←NoIsolationWorkerFactory$1.lambda$execute$0(NoIsolationWorkerFactory.java:62)←AbstractWorker$1.call(AbstractWorker.java:44)←AbstractWorker$1.call(AbstractWorker.java:41)←DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:209)←DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)←DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)←DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)←DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)←DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)←DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)←AbstractWorker.executeWrappedInBuildOperation(AbstractWorker.java:41)←NoIsolationWorkerFactory$1.execute(NoIsolationWorkerFactory.java:59)←DefaultWorkerExecutor.lambda$submitWork$0(DefaultWorkerExecutor.java:174)←FutureTask.run(FutureTask.java:317)←DefaultConditionalExecutionQueue$ExecutionRunner.runExecution(DefaultConditionalExecutionQueue.java:195)←DefaultConditionalExecutionQueue$ExecutionRunner.access$700(DefaultConditionalExecutionQueue.java:128)←DefaultConditionalExecutionQueue$ExecutionRunner$1.run(DefaultConditionalExecutionQueue.java:170)←Factories$1.create(Factories.java:31)←DefaultWorkerLeaseService.withLocks(DefaultWorkerLeaseService.java:267)←DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:131)←DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:136)←DefaultConditionalExecutionQueue$ExecutionRunner.runBatch(DefaultConditionalExecutionQueue.java:165)←DefaultConditionalExecutionQueue$ExecutionRunner.run(DefaultConditionalExecutionQueue.java:134)←Executors$RunnableAdapter.call(Executors.java:572)←FutureTask.run(FutureTask.java:317)←ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)←AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:48)←ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)←ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)←Thread.run(Thread.java:1583)\`
     Stack: `RuntimeException:LintDriver$Companion.handleDetectorError(LintDriver.kt:3740)←LintDriver$Companion.handleDetectorError$default(LintDriver.kt:3607)←LintDriver$Companion.handleDetectorError(LintDriver.kt:3603)←LintDriver$analyzeOnly$1.invoke(LintDriver.kt:447)←LintDriver$analyzeOnly$1.invoke(LintDriver.kt:442)←LintDriver.doAnalyze(LintDriver.kt:501)←LintDriver.analyzeOnly(LintDriver.kt:442)←LintCliClient$analyzeOnly$1.invoke(LintCliClient.kt:258)←LintCliClient$analyzeOnly$1.invoke(LintCliClient.kt:255)←LintCliClient.run(LintCliClient.kt:312)←LintCliClient.analyzeOnly(LintCliClient.kt:255)←Main.run(Main.java:1766)←Main.run(Main.java:282)←DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)←Method.invoke(Method.java:580)←AndroidLintWorkAction.invokeLintMainRunMethod(AndroidLintWorkAction.kt:103)←AndroidLintWorkAction.runLint(AndroidLintWorkAction.kt:90)←AndroidLintWorkAction.execute(AndroidLintWorkAction.kt:64)←DefaultWorkerServer.execute(DefaultWorkerServer.java:63)←NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:66)←NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:62)←ClassLoaderUtils.executeInClassloader(ClassLoaderUtils.java:100)←NoIsolationWorkerFactory$1.lambda$execute$0(NoIsolationWorkerFactory.java:62)←AbstractWorker$1.call(AbstractWorker.java:44)←AbstractWorker$1.call(AbstractWorker.java:41)←DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:209)←DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)←DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)←DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)←DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)←DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)←DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)←AbstractWorker.executeWrappedInBuildOperation(AbstractWorker.java:41)←NoIsolationWorkerFactory$1.execute(NoIsolationWorkerFactory.java:59)←DefaultWorkerExecutor.lambda$submitWork$0(DefaultWorkerExecutor.java:174)←FutureTask.run(FutureTask.java:317)←DefaultConditionalExecutionQueue$ExecutionRunner.runExecution(DefaultConditionalExecutionQueue.java:195)←DefaultConditionalExecutionQueue$ExecutionRunner.access$700(DefaultConditionalExecutionQueue.java:128)←DefaultConditionalExecutionQueue$ExecutionRunner$1.run(DefaultConditionalExecutionQueue.java:170)←Factories$1.create(Factories.java:31)←DefaultWorkerLeaseService.withLocks(DefaultWorkerLeaseService.java:267)←DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:131)←DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:136)←DefaultConditionalExecutionQueue$ExecutionRunner.runBatch(DefaultConditionalExecutionQueue.java:165)←DefaultConditionalExecutionQueue$ExecutionRunner.run(DefaultConditionalExecutionQueue.java:134)←Executors$RunnableAdapter.call(Executors.java:572)←FutureTask.run(FutureTask.java:317)←ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)←AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:48)←ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)←ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)←Thread.run(Thread.java:1583)`

* Try:
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

* Exception is:
org.gradle.api.tasks.TaskExecutionException: Execution failed for task ':compose-ui:lintAnalyzeDebug'.
	at org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.lambda$executeIfValid$1(ExecuteActionsTaskExecuter.java:130)
	at org.gradle.internal.Try$Failure.ifSuccessfulOrElse(Try.java:293)
	at org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.executeIfValid(ExecuteActionsTaskExecuter.java:128)
	at org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.execute(ExecuteActionsTaskExecuter.java:116)
	at org.gradle.api.internal.tasks.execution.FinalizePropertiesTaskExecuter.execute(FinalizePropertiesTaskExecuter.java:46)
	at org.gradle.api.internal.tasks.execution.ResolveTaskExecutionModeExecuter.execute(ResolveTaskExecutionModeExecuter.java:51)
	at org.gradle.api.internal.tasks.execution.SkipTaskWithNoActionsExecuter.execute(SkipTaskWithNoActionsExecuter.java:57)
	at org.gradle.api.internal.tasks.execution.SkipOnlyIfTaskExecuter.execute(SkipOnlyIfTaskExecuter.java:74)
	at org.gradle.api.internal.tasks.execution.CatchExceptionTaskExecuter.execute(CatchExceptionTaskExecuter.java:36)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.executeTask(EventFiringTaskExecuter.java:77)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.call(EventFiringTaskExecuter.java:55)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.call(EventFiringTaskExecuter.java:52)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:209)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter.execute(EventFiringTaskExecuter.java:52)
	at org.gradle.execution.plan.LocalTaskNodeExecutor.execute(LocalTaskNodeExecutor.java:42)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$InvokeNodeExecutorsAction.execute(DefaultTaskExecutionGraph.java:331)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$InvokeNodeExecutorsAction.execute(DefaultTaskExecutionGraph.java:318)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.lambda$execute$0(DefaultTaskExecutionGraph.java:314)
	at org.gradle.internal.operations.CurrentBuildOperationRef.with(CurrentBuildOperationRef.java:85)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.execute(DefaultTaskExecutionGraph.java:314)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.execute(DefaultTaskExecutionGraph.java:303)
	at org.gradle.execution.plan.DefaultPlanExecutor$ExecutorWorker.execute(DefaultPlanExecutor.java:459)
	at org.gradle.execution.plan.DefaultPlanExecutor$ExecutorWorker.run(DefaultPlanExecutor.java:376)
	at org.gradle.internal.concurrent.ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)
	at org.gradle.internal.concurrent.AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:48)
Caused by: org.gradle.workers.internal.DefaultWorkerExecutor$WorkExecutionException: A failure occurred while executing com.android.build.gradle.internal.lint.AndroidLintWorkAction
	at org.gradle.workers.internal.DefaultWorkerExecutor$WorkItemExecution.waitForCompletion(DefaultWorkerExecutor.java:287)
	at org.gradle.internal.work.DefaultAsyncWorkTracker.lambda$waitForItemsAndGatherFailures$2(DefaultAsyncWorkTracker.java:130)
	at org.gradle.internal.Factories$1.create(Factories.java:31)
	at org.gradle.internal.work.DefaultWorkerLeaseService.withoutLocks(DefaultWorkerLeaseService.java:339)
	at org.gradle.internal.work.DefaultWorkerLeaseService.withoutLocks(DefaultWorkerLeaseService.java:322)
	at org.gradle.internal.work.DefaultWorkerLeaseService.withoutLock(DefaultWorkerLeaseService.java:327)
	at org.gradle.internal.work.DefaultAsyncWorkTracker.waitForItemsAndGatherFailures(DefaultAsyncWorkTracker.java:126)
	at org.gradle.internal.work.DefaultAsyncWorkTracker.waitForItemsAndGatherFailures(DefaultAsyncWorkTracker.java:92)
	at org.gradle.internal.work.DefaultAsyncWorkTracker.waitForAll(DefaultAsyncWorkTracker.java:78)
	at org.gradle.internal.work.DefaultAsyncWorkTracker.waitForCompletion(DefaultAsyncWorkTracker.java:66)
	at org.gradle.api.internal.tasks.execution.TaskExecution$3.run(TaskExecution.java:252)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:29)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:26)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.run(DefaultBuildOperationRunner.java:47)
	at org.gradle.api.internal.tasks.execution.TaskExecution.executeAction(TaskExecution.java:229)
	at org.gradle.api.internal.tasks.execution.TaskExecution.executeActions(TaskExecution.java:212)
	at org.gradle.api.internal.tasks.execution.TaskExecution.executeWithPreviousOutputFiles(TaskExecution.java:195)
	at org.gradle.api.internal.tasks.execution.TaskExecution.execute(TaskExecution.java:162)
	at org.gradle.internal.execution.steps.ExecuteStep.executeInternal(ExecuteStep.java:105)
	at org.gradle.internal.execution.steps.ExecuteStep.access$000(ExecuteStep.java:44)
	at org.gradle.internal.execution.steps.ExecuteStep$1.call(ExecuteStep.java:59)
	at org.gradle.internal.execution.steps.ExecuteStep$1.call(ExecuteStep.java:56)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:209)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)
	at org.gradle.internal.execution.steps.ExecuteStep.execute(ExecuteStep.java:56)
	at org.gradle.internal.execution.steps.ExecuteStep.execute(ExecuteStep.java:44)
	at org.gradle.internal.execution.steps.CancelExecutionStep.execute(CancelExecutionStep.java:42)
	at org.gradle.internal.execution.steps.TimeoutStep.executeWithoutTimeout(TimeoutStep.java:75)
	at org.gradle.internal.execution.steps.TimeoutStep.execute(TimeoutStep.java:55)
	at org.gradle.internal.execution.steps.PreCreateOutputParentsStep.execute(PreCreateOutputParentsStep.java:50)
	at org.gradle.internal.execution.steps.PreCreateOutputParentsStep.execute(PreCreateOutputParentsStep.java:28)
	at org.gradle.internal.execution.steps.RemovePreviousOutputsStep.execute(RemovePreviousOutputsStep.java:67)
	at org.gradle.internal.execution.steps.RemovePreviousOutputsStep.execute(RemovePreviousOutputsStep.java:37)
	at org.gradle.internal.execution.steps.BroadcastChangingOutputsStep.execute(BroadcastChangingOutputsStep.java:61)
	at org.gradle.internal.execution.steps.BroadcastChangingOutputsStep.execute(BroadcastChangingOutputsStep.java:26)
	at org.gradle.internal.execution.steps.CaptureOutputsAfterExecutionStep.execute(CaptureOutputsAfterExecutionStep.java:69)
	at org.gradle.internal.execution.steps.CaptureOutputsAfterExecutionStep.execute(CaptureOutputsAfterExecutionStep.java:46)
	at org.gradle.internal.execution.steps.ResolveInputChangesStep.execute(ResolveInputChangesStep.java:40)
	at org.gradle.internal.execution.steps.ResolveInputChangesStep.execute(ResolveInputChangesStep.java:29)
	at org.gradle.internal.execution.steps.BuildCacheStep.executeWithoutCache(BuildCacheStep.java:189)
	at org.gradle.internal.execution.steps.BuildCacheStep.executeAndStoreInCache(BuildCacheStep.java:145)
	at org.gradle.internal.execution.steps.BuildCacheStep.lambda$executeWithCache$4(BuildCacheStep.java:101)
	at org.gradle.internal.execution.steps.BuildCacheStep.lambda$executeWithCache$5(BuildCacheStep.java:101)
	at org.gradle.internal.Try$Success.map(Try.java:175)
	at org.gradle.internal.execution.steps.BuildCacheStep.executeWithCache(BuildCacheStep.java:85)
	at org.gradle.internal.execution.steps.BuildCacheStep.lambda$execute$0(BuildCacheStep.java:74)
	at org.gradle.internal.Either$Left.fold(Either.java:115)
	at org.gradle.internal.execution.caching.CachingState.fold(CachingState.java:62)
	at org.gradle.internal.execution.steps.BuildCacheStep.execute(BuildCacheStep.java:73)
	at org.gradle.internal.execution.steps.BuildCacheStep.execute(BuildCacheStep.java:48)
	at org.gradle.internal.execution.steps.StoreExecutionStateStep.execute(StoreExecutionStateStep.java:46)
	at org.gradle.internal.execution.steps.StoreExecutionStateStep.execute(StoreExecutionStateStep.java:35)
	at org.gradle.internal.execution.steps.SkipUpToDateStep.executeBecause(SkipUpToDateStep.java:75)
	at org.gradle.internal.execution.steps.SkipUpToDateStep.lambda$execute$2(SkipUpToDateStep.java:53)
	at org.gradle.internal.execution.steps.SkipUpToDateStep.execute(SkipUpToDateStep.java:53)
	at org.gradle.internal.execution.steps.SkipUpToDateStep.execute(SkipUpToDateStep.java:35)
	at org.gradle.internal.execution.steps.legacy.MarkSnapshottingInputsFinishedStep.execute(MarkSnapshottingInputsFinishedStep.java:37)
	at org.gradle.internal.execution.steps.legacy.MarkSnapshottingInputsFinishedStep.execute(MarkSnapshottingInputsFinishedStep.java:27)
	at org.gradle.internal.execution.steps.ResolveIncrementalCachingStateStep.executeDelegate(ResolveIncrementalCachingStateStep.java:49)
	at org.gradle.internal.execution.steps.ResolveIncrementalCachingStateStep.executeDelegate(ResolveIncrementalCachingStateStep.java:27)
	at org.gradle.internal.execution.steps.AbstractResolveCachingStateStep.execute(AbstractResolveCachingStateStep.java:71)
	at org.gradle.internal.execution.steps.AbstractResolveCachingStateStep.execute(AbstractResolveCachingStateStep.java:39)
	at org.gradle.internal.execution.steps.ResolveChangesStep.execute(ResolveChangesStep.java:65)
	at org.gradle.internal.execution.steps.ResolveChangesStep.execute(ResolveChangesStep.java:36)
	at org.gradle.internal.execution.steps.ValidateStep.execute(ValidateStep.java:105)
	at org.gradle.internal.execution.steps.ValidateStep.execute(ValidateStep.java:54)
	at org.gradle.internal.execution.steps.AbstractCaptureStateBeforeExecutionStep.execute(AbstractCaptureStateBeforeExecutionStep.java:64)
	at org.gradle.internal.execution.steps.AbstractCaptureStateBeforeExecutionStep.execute(AbstractCaptureStateBeforeExecutionStep.java:43)
	at org.gradle.internal.execution.steps.AbstractSkipEmptyWorkStep.executeWithNonEmptySources(AbstractSkipEmptyWorkStep.java:125)
	at org.gradle.internal.execution.steps.AbstractSkipEmptyWorkStep.execute(AbstractSkipEmptyWorkStep.java:56)
	at org.gradle.internal.execution.steps.AbstractSkipEmptyWorkStep.execute(AbstractSkipEmptyWorkStep.java:36)
	at org.gradle.internal.execution.steps.legacy.MarkSnapshottingInputsStartedStep.execute(MarkSnapshottingInputsStartedStep.java:38)
	at org.gradle.internal.execution.steps.LoadPreviousExecutionStateStep.execute(LoadPreviousExecutionStateStep.java:36)
	at org.gradle.internal.execution.steps.LoadPreviousExecutionStateStep.execute(LoadPreviousExecutionStateStep.java:23)
	at org.gradle.internal.execution.steps.HandleStaleOutputsStep.execute(HandleStaleOutputsStep.java:75)
	at org.gradle.internal.execution.steps.HandleStaleOutputsStep.execute(HandleStaleOutputsStep.java:41)
	at org.gradle.internal.execution.steps.AssignMutableWorkspaceStep.lambda$execute$0(AssignMutableWorkspaceStep.java:35)
	at org.gradle.api.internal.tasks.execution.TaskExecution$4.withWorkspace(TaskExecution.java:289)
	at org.gradle.internal.execution.steps.AssignMutableWorkspaceStep.execute(AssignMutableWorkspaceStep.java:31)
	at org.gradle.internal.execution.steps.AssignMutableWorkspaceStep.execute(AssignMutableWorkspaceStep.java:22)
	at org.gradle.internal.execution.steps.ChoosePipelineStep.execute(ChoosePipelineStep.java:40)
	at org.gradle.internal.execution.steps.ChoosePipelineStep.execute(ChoosePipelineStep.java:23)
	at org.gradle.internal.execution.steps.ExecuteWorkBuildOperationFiringStep.lambda$execute$2(ExecuteWorkBuildOperationFiringStep.java:67)
	at org.gradle.internal.execution.steps.ExecuteWorkBuildOperationFiringStep.execute(ExecuteWorkBuildOperationFiringStep.java:67)
	at org.gradle.internal.execution.steps.ExecuteWorkBuildOperationFiringStep.execute(ExecuteWorkBuildOperationFiringStep.java:39)
	at org.gradle.internal.execution.steps.IdentityCacheStep.execute(IdentityCacheStep.java:46)
	at org.gradle.internal.execution.steps.IdentityCacheStep.execute(IdentityCacheStep.java:34)
	at org.gradle.internal.execution.steps.IdentifyStep.execute(IdentifyStep.java:48)
	at org.gradle.internal.execution.steps.IdentifyStep.execute(IdentifyStep.java:35)
	at org.gradle.internal.execution.impl.DefaultExecutionEngine$1.execute(DefaultExecutionEngine.java:61)
	at org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.executeIfValid(ExecuteActionsTaskExecuter.java:127)
	at org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.execute(ExecuteActionsTaskExecuter.java:116)
	at org.gradle.api.internal.tasks.execution.FinalizePropertiesTaskExecuter.execute(FinalizePropertiesTaskExecuter.java:46)
	at org.gradle.api.internal.tasks.execution.ResolveTaskExecutionModeExecuter.execute(ResolveTaskExecutionModeExecuter.java:51)
	at org.gradle.api.internal.tasks.execution.SkipTaskWithNoActionsExecuter.execute(SkipTaskWithNoActionsExecuter.java:57)
	at org.gradle.api.internal.tasks.execution.SkipOnlyIfTaskExecuter.execute(SkipOnlyIfTaskExecuter.java:74)
	at org.gradle.api.internal.tasks.execution.CatchExceptionTaskExecuter.execute(CatchExceptionTaskExecuter.java:36)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.executeTask(EventFiringTaskExecuter.java:77)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.call(EventFiringTaskExecuter.java:55)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.call(EventFiringTaskExecuter.java:52)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:209)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter.execute(EventFiringTaskExecuter.java:52)
	at org.gradle.execution.plan.LocalTaskNodeExecutor.execute(LocalTaskNodeExecutor.java:42)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$InvokeNodeExecutorsAction.execute(DefaultTaskExecutionGraph.java:331)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$InvokeNodeExecutorsAction.execute(DefaultTaskExecutionGraph.java:318)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.lambda$execute$0(DefaultTaskExecutionGraph.java:314)
	at org.gradle.internal.operations.CurrentBuildOperationRef.with(CurrentBuildOperationRef.java:85)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.execute(DefaultTaskExecutionGraph.java:314)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.execute(DefaultTaskExecutionGraph.java:303)
	at org.gradle.execution.plan.DefaultPlanExecutor$ExecutorWorker.execute(DefaultPlanExecutor.java:459)
	at org.gradle.execution.plan.DefaultPlanExecutor$ExecutorWorker.run(DefaultPlanExecutor.java:376)
	at org.gradle.internal.concurrent.ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)
	at org.gradle.internal.concurrent.AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:48)
Caused by: java.lang.RuntimeException: Unexpected failure during lint analysis (this is a bug in lint or one of the libraries it depends on)

Message: Unexpected failure during lint analysis (this is a bug in lint or one of the libraries it depends on)

Message: Unexpected failure during lint analysis of CoreBackHandler.android.kt (this is a bug in lint or one of the libraries it depends on)

Message: Found class org.jetbrains.kotlin.analysis.api.resolution.KaCallableMemberCall, but interface was expected

The crash seems to involve the detector \\\`androidx.lifecycle.lint.NonNullableMutableLiveDataDetector\\\`.
You can try disabling it with something like this:
    android {
        lint {
            disable "NullSafeMutableLiveData"
        }
    }

Stack: \\\`IncompatibleClassChangeError:NonNullableMutableLiveDataDetector$createUastHandler$1.visitCallExpression(NonNullableMutableLiveDataDetector.kt:140)←UElementVisitor$DispatchUastVisitor.visitCallExpression(UElementVisitor.kt:412)←UElementVisitor$DelegatingUastVisitor.visitCallExpression(UElementVisitor.kt:756)←UCallExpression.accept(UCallExpression.kt:94)←ImplementationUtilsKt.acceptList(implementationUtils.kt:15)←UBlockExpression.accept(UBlockExpression.kt:21)←UMethod.accept(UMethod.kt:45)←ImplementationUtilsKt.acceptList(implementationUtils.kt:15)←AbstractKotlinUClass.accept(AbstractKotlinUClass.kt:213)←ImplementationUtilsKt.acceptList(implementationUtils.kt:15)←UFile.accept(UFile.kt:89)←UastLintUtilsKt.acceptSourceFile(UastLintUtils.kt:953)←UElementVisitor$visitFile$3.run(UElementVisitor.kt:216)←LintClient.runReadAction(LintClient.kt:1781)←LintDriver$LintClientWrapper.runReadAction(LintDriver.kt:2747)←UElementVisitor.visitFile(UElementVisitor.kt:213)←LintDriver$visitUastDetectors$1.run(LintDriver.kt:2048)←LintClient.runReadAction(LintClient.kt:1781)←LintDriver$LintClientWrapper.runReadAction(LintDriver.kt:2747)←LintDriver.visitUastDetectors(LintDriver.kt:2048)←LintDriver.visitUast(LintDriver.kt:2010)←LintDriver.runFileDetectors(LintDriver.kt:1284)←LintDriver.checkProject(LintDriver.kt:1070)←LintDriver.checkProjectRoot(LintDriver.kt:622)←LintDriver.access$checkProjectRoot(LintDriver.kt:174)←LintDriver$analyzeOnly$1.invoke(LintDriver.kt:445)←LintDriver$analyzeOnly$1.invoke(LintDriver.kt:442)←LintDriver.doAnalyze(LintDriver.kt:501)←LintDriver.analyzeOnly(LintDriver.kt:442)←LintCliClient$analyzeOnly$1.invoke(LintCliClient.kt:258)←LintCliClient$analyzeOnly$1.invoke(LintCliClient.kt:255)←LintCliClient.run(LintCliClient.kt:312)←LintCliClient.analyzeOnly(LintCliClient.kt:255)←Main.run(Main.java:1766)←Main.run(Main.java:282)←DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)←Method.invoke(Method.java:580)←AndroidLintWorkAction.invokeLintMainRunMethod(AndroidLintWorkAction.kt:103)←AndroidLintWorkAction.runLint(AndroidLintWorkAction.kt:90)←AndroidLintWorkAction.execute(AndroidLintWorkAction.kt:64)←DefaultWorkerServer.execute(DefaultWorkerServer.java:63)←NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:66)←NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:62)←ClassLoaderUtils.executeInClassloader(ClassLoaderUtils.java:100)←NoIsolationWorkerFactory$1.lambda$execute$0(NoIsolationWorkerFactory.java:62)←AbstractWorker$1.call(AbstractWorker.java:44)←AbstractWorker$1.call(AbstractWorker.java:41)←DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:209)←DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)←DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)←DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)←DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)←DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)←DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)←AbstractWorker.executeWrappedInBuildOperation(AbstractWorker.java:41)←NoIsolationWorkerFactory$1.execute(NoIsolationWorkerFactory.java:59)←DefaultWorkerExecutor.lambda$submitWork$0(DefaultWorkerExecutor.java:174)←FutureTask.run(FutureTask.java:317)←DefaultConditionalExecutionQueue$ExecutionRunner.runExecution(DefaultConditionalExecutionQueue.java:195)←DefaultConditionalExecutionQueue$ExecutionRunner.access$700(DefaultConditionalExecutionQueue.java:128)←DefaultConditionalExecutionQueue$ExecutionRunner$1.run(DefaultConditionalExecutionQueue.java:170)←Factories$1.create(Factories.java:31)←DefaultWorkerLeaseService.withLocks(DefaultWorkerLeaseService.java:267)←DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:131)←DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:136)←DefaultConditionalExecutionQueue$ExecutionRunner.runBatch(DefaultConditionalExecutionQueue.java:165)←DefaultConditionalExecutionQueue$ExecutionRunner.run(DefaultConditionalExecutionQueue.java:134)←Executors$RunnableAdapter.call(Executors.java:572)←FutureTask.run(FutureTask.java:317)←ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)←AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:48)←ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)←ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)←Thread.run(Thread.java:1583)\\\`
Stack: \`RuntimeException:LintDriver$Companion.handleDetectorError(LintDriver.kt:3740)←LintDriver$Companion.handleDetectorError$default(LintDriver.kt:3607)←LintDriver$Companion.handleDetectorError(LintDriver.kt:3603)←UElementVisitor.visitFile(UElementVisitor.kt:242)←LintDriver$visitUastDetectors$1.run(LintDriver.kt:2048)←LintClient.runReadAction(LintClient.kt:1781)←LintDriver$LintClientWrapper.runReadAction(LintDriver.kt:2747)←LintDriver.visitUastDetectors(LintDriver.kt:2048)←LintDriver.visitUast(LintDriver.kt:2010)←LintDriver.runFileDetectors(LintDriver.kt:1284)←LintDriver.checkProject(LintDriver.kt:1070)←LintDriver.checkProjectRoot(LintDriver.kt:622)←LintDriver.access$checkProjectRoot(LintDriver.kt:174)←LintDriver$analyzeOnly$1.invoke(LintDriver.kt:445)←LintDriver$analyzeOnly$1.invoke(LintDriver.kt:442)←LintDriver.doAnalyze(LintDriver.kt:501)←LintDriver.analyzeOnly(LintDriver.kt:442)←LintCliClient$analyzeOnly$1.invoke(LintCliClient.kt:258)←LintCliClient$analyzeOnly$1.invoke(LintCliClient.kt:255)←LintCliClient.run(LintCliClient.kt:312)←LintCliClient.analyzeOnly(LintCliClient.kt:255)←Main.run(Main.java:1766)←Main.run(Main.java:282)←DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)←Method.invoke(Method.java:580)←AndroidLintWorkAction.invokeLintMainRunMethod(AndroidLintWorkAction.kt:103)←AndroidLintWorkAction.runLint(AndroidLintWorkAction.kt:90)←AndroidLintWorkAction.execute(AndroidLintWorkAction.kt:64)←DefaultWorkerServer.execute(DefaultWorkerServer.java:63)←NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:66)←NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:62)←ClassLoaderUtils.executeInClassloader(ClassLoaderUtils.java:100)←NoIsolationWorkerFactory$1.lambda$execute$0(NoIsolationWorkerFactory.java:62)←AbstractWorker$1.call(AbstractWorker.java:44)←AbstractWorker$1.call(AbstractWorker.java:41)←DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:209)←DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)←DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)←DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)←DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)←DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)←DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)←AbstractWorker.executeWrappedInBuildOperation(AbstractWorker.java:41)←NoIsolationWorkerFactory$1.execute(NoIsolationWorkerFactory.java:59)←DefaultWorkerExecutor.lambda$submitWork$0(DefaultWorkerExecutor.java:174)←FutureTask.run(FutureTask.java:317)←DefaultConditionalExecutionQueue$ExecutionRunner.runExecution(DefaultConditionalExecutionQueue.java:195)←DefaultConditionalExecutionQueue$ExecutionRunner.access$700(DefaultConditionalExecutionQueue.java:128)←DefaultConditionalExecutionQueue$ExecutionRunner$1.run(DefaultConditionalExecutionQueue.java:170)←Factories$1.create(Factories.java:31)←DefaultWorkerLeaseService.withLocks(DefaultWorkerLeaseService.java:267)←DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:131)←DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:136)←DefaultConditionalExecutionQueue$ExecutionRunner.runBatch(DefaultConditionalExecutionQueue.java:165)←DefaultConditionalExecutionQueue$ExecutionRunner.run(DefaultConditionalExecutionQueue.java:134)←Executors$RunnableAdapter.call(Executors.java:572)←FutureTask.run(FutureTask.java:317)←ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)←AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:48)←ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)←ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)←Thread.run(Thread.java:1583)\`
Stack: `RuntimeException:LintDriver$Companion.handleDetectorError(LintDriver.kt:3740)←LintDriver$Companion.handleDetectorError$default(LintDriver.kt:3607)←LintDriver$Companion.handleDetectorError(LintDriver.kt:3603)←LintDriver$analyzeOnly$1.invoke(LintDriver.kt:447)←LintDriver$analyzeOnly$1.invoke(LintDriver.kt:442)←LintDriver.doAnalyze(LintDriver.kt:501)←LintDriver.analyzeOnly(LintDriver.kt:442)←LintCliClient$analyzeOnly$1.invoke(LintCliClient.kt:258)←LintCliClient$analyzeOnly$1.invoke(LintCliClient.kt:255)←LintCliClient.run(LintCliClient.kt:312)←LintCliClient.analyzeOnly(LintCliClient.kt:255)←Main.run(Main.java:1766)←Main.run(Main.java:282)←DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)←Method.invoke(Method.java:580)←AndroidLintWorkAction.invokeLintMainRunMethod(AndroidLintWorkAction.kt:103)←AndroidLintWorkAction.runLint(AndroidLintWorkAction.kt:90)←AndroidLintWorkAction.execute(AndroidLintWorkAction.kt:64)←DefaultWorkerServer.execute(DefaultWorkerServer.java:63)←NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:66)←NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:62)←ClassLoaderUtils.executeInClassloader(ClassLoaderUtils.java:100)←NoIsolationWorkerFactory$1.lambda$execute$0(NoIsolationWorkerFactory.java:62)←AbstractWorker$1.call(AbstractWorker.java:44)←AbstractWorker$1.call(AbstractWorker.java:41)←DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:209)←DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)←DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)←DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)←DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)←DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)←DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)←AbstractWorker.executeWrappedInBuildOperation(AbstractWorker.java:41)←NoIsolationWorkerFactory$1.execute(NoIsolationWorkerFactory.java:59)←DefaultWorkerExecutor.lambda$submitWork$0(DefaultWorkerExecutor.java:174)←FutureTask.run(FutureTask.java:317)←DefaultConditionalExecutionQueue$ExecutionRunner.runExecution(DefaultConditionalExecutionQueue.java:195)←DefaultConditionalExecutionQueue$ExecutionRunner.access$700(DefaultConditionalExecutionQueue.java:128)←DefaultConditionalExecutionQueue$ExecutionRunner$1.run(DefaultConditionalExecutionQueue.java:170)←Factories$1.create(Factories.java:31)←DefaultWorkerLeaseService.withLocks(DefaultWorkerLeaseService.java:267)←DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:131)←DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:136)←DefaultConditionalExecutionQueue$ExecutionRunner.runBatch(DefaultConditionalExecutionQueue.java:165)←DefaultConditionalExecutionQueue$ExecutionRunner.run(DefaultConditionalExecutionQueue.java:134)←Executors$RunnableAdapter.call(Executors.java:572)←FutureTask.run(FutureTask.java:317)←ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)←AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:48)←ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)←ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)←Thread.run(Thread.java:1583)`
	at com.android.tools.lint.client.api.LintDriver$Companion.handleDetectorError(LintDriver.kt:3740)
	at com.android.tools.lint.client.api.LintDriver$Companion.handleDetectorError$default(LintDriver.kt:3607)
	at com.android.tools.lint.client.api.LintDriver$Companion.handleDetectorError(LintDriver.kt:3603)
	at com.android.tools.lint.client.api.LintDriver.doAnalyze(LintDriver.kt:503)
	at com.android.tools.lint.client.api.LintDriver.analyzeOnly(LintDriver.kt:442)
	at com.android.tools.lint.LintCliClient$analyzeOnly$1.invoke(LintCliClient.kt:258)
	at com.android.tools.lint.LintCliClient$analyzeOnly$1.invoke(LintCliClient.kt:255)
	at com.android.tools.lint.LintCliClient.run(LintCliClient.kt:312)
	at com.android.tools.lint.LintCliClient.analyzeOnly(LintCliClient.kt:255)
	at com.android.tools.lint.Main.run(Main.java:1766)
	at com.android.tools.lint.Main.run(Main.java:282)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at com.android.build.gradle.internal.lint.AndroidLintWorkAction.invokeLintMainRunMethod(AndroidLintWorkAction.kt:103)
	at com.android.build.gradle.internal.lint.AndroidLintWorkAction.runLint(AndroidLintWorkAction.kt:90)
	at com.android.build.gradle.internal.lint.AndroidLintWorkAction.execute(AndroidLintWorkAction.kt:64)
	at org.gradle.workers.internal.DefaultWorkerServer.execute(DefaultWorkerServer.java:63)
	at org.gradle.workers.internal.NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:66)
	at org.gradle.workers.internal.NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:62)
	at org.gradle.internal.classloader.ClassLoaderUtils.executeInClassloader(ClassLoaderUtils.java:100)
	at org.gradle.workers.internal.NoIsolationWorkerFactory$1.lambda$execute$0(NoIsolationWorkerFactory.java:62)
	at org.gradle.workers.internal.AbstractWorker$1.call(AbstractWorker.java:44)
	at org.gradle.workers.internal.AbstractWorker$1.call(AbstractWorker.java:41)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:209)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)
	at org.gradle.workers.internal.AbstractWorker.executeWrappedInBuildOperation(AbstractWorker.java:41)
	at org.gradle.workers.internal.NoIsolationWorkerFactory$1.execute(NoIsolationWorkerFactory.java:59)
	at org.gradle.workers.internal.DefaultWorkerExecutor.lambda$submitWork$0(DefaultWorkerExecutor.java:174)
	at org.gradle.internal.work.DefaultConditionalExecutionQueue$ExecutionRunner.runExecution(DefaultConditionalExecutionQueue.java:195)
	at org.gradle.internal.work.DefaultConditionalExecutionQueue$ExecutionRunner.access$700(DefaultConditionalExecutionQueue.java:128)
	at org.gradle.internal.work.DefaultConditionalExecutionQueue$ExecutionRunner$1.run(DefaultConditionalExecutionQueue.java:170)
254 actionable tasks: 230 executed, 24 from cache
	at org.gradle.internal.Factories$1.create(Factories.java:31)
	at org.gradle.internal.work.DefaultWorkerLeaseService.withLocks(DefaultWorkerLeaseService.java:267)
	at org.gradle.internal.work.DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:131)
	at org.gradle.internal.work.DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:136)
	at org.gradle.internal.work.DefaultConditionalExecutionQueue$ExecutionRunner.runBatch(DefaultConditionalExecutionQueue.java:165)
	at org.gradle.internal.work.DefaultConditionalExecutionQueue$ExecutionRunner.run(DefaultConditionalExecutionQueue.java:134)
	... 2 more


BUILD FAILED in 3m 55s
Configuration cache entry stored.
Skipping cache upload for failed step
Searching for files matching artifact pattern **/build/reports/lint-results*
Artifact pattern **/build/reports/lint-results* matched 0 files


Searching for test report files in directories named [test-results, failsafe-reports, test-reports, TestResults, surefire-reports] down to a depth of 4
Finished scanning for test reports. Found 0 test report files.
Merged test suites, total number tests is 0, with 0 failures and 0 errors.
