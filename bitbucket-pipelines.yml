# Bitbucket Pipeline for QuadHubKMP - Kotlin Multiplatform Project
# This pipeline builds and tests the Android debug app

image: openjdk:21

definitions:
  caches:
    gradle: ~/.gradle/caches
    gradle-wrapper: ~/.gradle/wrapper
  services:
    docker:
      memory: 4096

pipelines:
  default:
    - parallel:
      - step:
          name: 'Build and Test Android Debug'
          size: 2x
          caches:
            - gradle
            - gradle-wrapper
          script:
            # Install Android SDK
            - apt-get update && apt-get install -y wget unzip
            - mkdir -p /opt/android-sdk
            - cd /opt/android-sdk
            - wget -q https://dl.google.com/android/repository/commandlinetools-linux-11076708_latest.zip
            - unzip -q commandlinetools-linux-11076708_latest.zip
            - mkdir -p cmdline-tools/latest
            - mv cmdline-tools/* cmdline-tools/latest/ 2>/dev/null || true
            - export ANDROID_HOME=/opt/android-sdk
            - export ANDROID_SDK_ROOT=/opt/android-sdk
            - export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools
            
            # Accept Android SDK licenses
            - yes | sdkmanager --licenses > /dev/null 2>&1 || true
            
            # Install required Android SDK components
            - sdkmanager "platform-tools" "platforms;android-36" "build-tools;35.0.0"
            
            # Navigate back to project directory
            - cd $BITBUCKET_CLONE_DIR
            
            # Make gradlew executable
            - chmod +x ./gradlew
            
            # Build the debug app
            - ./gradlew composeApp:assembleDebug --no-daemon --stacktrace
            
            # Run tests
            - ./gradlew test --no-daemon --stacktrace
          artifacts:
            - composeApp/build/outputs/**
            - "**/build/reports/**"
            
      - step:
          name: 'Lint'
          size: 2x
          caches:
            - gradle
            - gradle-wrapper
          script:
            # Install Android SDK (minimal setup for linting)
            - apt-get update && apt-get install -y wget unzip
            - mkdir -p /opt/android-sdk
            - cd /opt/android-sdk
            - wget -q https://dl.google.com/android/repository/commandlinetools-linux-11076708_latest.zip
            - unzip -q commandlinetools-linux-11076708_latest.zip
            - mkdir -p cmdline-tools/latest
            - mv cmdline-tools/* cmdline-tools/latest/ 2>/dev/null || true
            - export ANDROID_HOME=/opt/android-sdk
            - export ANDROID_SDK_ROOT=/opt/android-sdk
            - export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin
            
            # Accept licenses and install minimal components
            - yes | sdkmanager --licenses > /dev/null 2>&1 || true
            - sdkmanager "platform-tools" "platforms;android-36"
            
            # Navigate back to project directory
            - cd $BITBUCKET_CLONE_DIR
            
            # Make gradlew executable
            - chmod +x ./gradlew
            
            # Run lint checks
            - ./gradlew lint --no-daemon --stacktrace
          artifacts:
            - "**/build/reports/lint-results*"
            
      - step:
          name: 'Security scan'
          script:
            # Basic security scanning - you can enhance this with specific tools
            - echo "Running basic security checks..."
            
            # Check for common security issues in gradle files
            - echo "Checking for hardcoded secrets in gradle files..."
            - find . -name "*.gradle*" -exec grep -l "password\|secret\|key\|token" {} \; || echo "No obvious secrets found in gradle files"
            
            # Check for TODO/FIXME comments that might indicate security issues
            - echo "Checking for security-related TODOs..."
            - find . -name "*.kt" -exec grep -n "TODO.*security\|FIXME.*security\|TODO.*auth\|FIXME.*auth" {} \; || echo "No security-related TODOs found"
            
            # You can add more sophisticated security scanning tools here
            - echo "Security scan completed. Consider integrating tools like:"
            - echo "- OWASP Dependency Check"
            - echo "- Snyk"
            - echo "- SonarQube Security"

    # Deployment steps - only run on main branch
    - step:
        name: 'Deployment to Staging'
        deployment: staging
        trigger: manual
        condition:
          changesets:
            includePaths:
              - "**"
        script:
          - echo "Deploying to staging environment..."
          - echo "Add your staging deployment commands here"
          # Example: Deploy APK to Firebase App Distribution, TestFlight, etc.
          
    - step:
        name: 'Deployment to Production'
        deployment: production
        trigger: manual
        condition:
          changesets:
            includePaths:
              - "**"
        script:
          - echo "Deploying to production environment..."
          - echo "Add your production deployment commands here"
          # Example: Deploy to Google Play Store, App Store, etc.

  branches:
    main:
      - parallel:
        - step:
            name: 'Build and Test Android Debug (Main Branch)'
            size: 2x
            caches:
              - gradle
              - gradle-wrapper
            script:
              # Install Android SDK
              - apt-get update && apt-get install -y wget unzip
              - mkdir -p /opt/android-sdk
              - cd /opt/android-sdk
              - wget -q https://dl.google.com/android/repository/commandlinetools-linux-11076708_latest.zip
              - unzip -q commandlinetools-linux-11076708_latest.zip
              - mkdir -p cmdline-tools/latest
              - mv cmdline-tools/* cmdline-tools/latest/ 2>/dev/null || true
              - export ANDROID_HOME=/opt/android-sdk
              - export ANDROID_SDK_ROOT=/opt/android-sdk
              - export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools
              
              # Accept Android SDK licenses
              - yes | sdkmanager --licenses > /dev/null 2>&1 || true
              
              # Install required Android SDK components
              - sdkmanager "platform-tools" "platforms;android-36" "build-tools;35.0.0"
              
              # Navigate back to project directory
              - cd $BITBUCKET_CLONE_DIR
              
              # Make gradlew executable
              - chmod +x ./gradlew
              
              # Build the debug app
              - ./gradlew composeApp:assembleDebug --no-daemon --stacktrace
              
              # Run tests
              - ./gradlew test --no-daemon --stacktrace
            artifacts:
              - composeApp/build/outputs/**
              - "**/build/reports/**"
              
        - step:
            name: 'Lint (Main Branch)'
            size: 2x
            caches:
              - gradle
              - gradle-wrapper
            script:
              # Install Android SDK (minimal setup for linting)
              - apt-get update && apt-get install -y wget unzip
              - mkdir -p /opt/android-sdk
              - cd /opt/android-sdk
              - wget -q https://dl.google.com/android/repository/commandlinetools-linux-11076708_latest.zip
              - unzip -q commandlinetools-linux-11076708_latest.zip
              - mkdir -p cmdline-tools/latest
              - mv cmdline-tools/* cmdline-tools/latest/ 2>/dev/null || true
              - export ANDROID_HOME=/opt/android-sdk
              - export ANDROID_SDK_ROOT=/opt/android-sdk
              - export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin
              
              # Accept licenses and install minimal components
              - yes | sdkmanager --licenses > /dev/null 2>&1 || true
              - sdkmanager "platform-tools" "platforms;android-36"
              
              # Navigate back to project directory
              - cd $BITBUCKET_CLONE_DIR
              
              # Make gradlew executable
              - chmod +x ./gradlew
              
              # Run lint checks
              - ./gradlew lint --no-daemon --stacktrace
            artifacts:
              - "**/build/reports/lint-results*"

      # Deployment steps for main branch
      - step:
          name: 'Deployment to Staging (Main Branch)'
          deployment: staging
          script:
            - echo "Auto-deploying main branch to staging..."
            - echo "Add your staging deployment commands here"
            
      - step:
          name: 'Deployment to Production (Main Branch)'
          deployment: production
          trigger: manual
          script:
            - echo "Manual deployment to production from main branch..."
            - echo "Add your production deployment commands here"
